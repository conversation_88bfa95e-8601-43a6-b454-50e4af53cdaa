package com.shipment.erp.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.shipment.erp.model.ui.TreeMenuItem;
import com.shipment.erp.model.ui.TreeMenuData;
import com.shipment.erp.model.User;
import com.shipment.erp.security.SecurityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.prefs.Preferences;

/**
 * خدمة إدارة القائمة الشجرية
 * تدير حفظ واستعادة حالة القائمة والصلاحيات
 */
@Service
public class TreeMenuService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreeMenuService.class);
    
    private static final String MENU_CONFIG_FILE = "menu-config.json";
    private static final String USER_PREFERENCES_NODE = "ship-erp/menu";
    
    @Autowired
    private SecurityService securityService;
    
    private final ObjectMapper objectMapper;
    private final TreeMenuData menuData;
    private final Preferences userPreferences;
    
    /**
     * منشئ الخدمة
     */
    public TreeMenuService() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.menuData = new TreeMenuData();
        this.userPreferences = Preferences.userNode(USER_PREFERENCES_NODE);
        
        // تحميل إعدادات القائمة
        loadMenuConfiguration();
        
        logger.info("تم تهيئة خدمة القائمة الشجرية");
    }
    
    /**
     * الحصول على بيانات القائمة
     */
    public TreeMenuData getMenuData() {
        return menuData;
    }
    
    /**
     * تحديث صلاحيات القائمة للمستخدم الحالي
     */
    public void updateMenuPermissions() {
        try {
            User currentUser = securityService.getCurrentUser();
            if (currentUser != null) {
                Set<String> userPermissions = securityService.getUserPermissions(currentUser);
                Set<String> userRoles = securityService.getUserRoles(currentUser);
                
                menuData.updatePermissions(userPermissions, userRoles);
                
                logger.debug("تم تحديث صلاحيات القائمة للمستخدم: {}", currentUser.getUsername());
            }
        } catch (Exception e) {
            logger.error("خطأ في تحديث صلاحيات القائمة", e);
        }
    }
    
    /**
     * حفظ تكوين القائمة
     */
    public void saveMenuConfiguration() {
        try {
            Path configPath = getConfigPath();
            Map<String, Object> config = createMenuConfiguration();
            
            objectMapper.writerWithDefaultPrettyPrinter()
                      .writeValue(configPath.toFile(), config);
            
            logger.info("تم حفظ تكوين القائمة في: {}", configPath);
            
        } catch (Exception e) {
            logger.error("خطأ في حفظ تكوين القائمة", e);
        }
    }
    
    /**
     * تحميل تكوين القائمة
     */
    private void loadMenuConfiguration() {
        try {
            Path configPath = getConfigPath();
            
            if (Files.exists(configPath)) {
                Map<String, Object> config = objectMapper.readValue(
                    configPath.toFile(), Map.class);
                
                applyMenuConfiguration(config);
                
                logger.info("تم تحميل تكوين القائمة من: {}", configPath);
            } else {
                logger.info("لم يتم العثور على ملف تكوين القائمة، سيتم استخدام التكوين الافتراضي");
            }
            
        } catch (Exception e) {
            logger.error("خطأ في تحميل تكوين القائمة", e);
        }
    }
    
    /**
     * إنشاء تكوين القائمة
     */
    private Map<String, Object> createMenuConfiguration() {
        Map<String, Object> config = new HashMap<>();
        
        // معلومات عامة
        config.put("version", "1.0");
        config.put("lastModified", System.currentTimeMillis());
        
        // إعدادات القائمة
        Map<String, Object> menuSettings = new HashMap<>();
        menuSettings.put("showIcons", true);
        menuSettings.put("showTooltips", true);
        menuSettings.put("searchEnabled", true);
        menuSettings.put("autoExpand", true);
        config.put("settings", menuSettings);
        
        // حالة العناصر
        List<Map<String, Object>> itemsState = new ArrayList<>();
        for (TreeMenuItem item : menuData.getRootItems()) {
            itemsState.add(createItemState(item));
        }
        config.put("itemsState", itemsState);
        
        return config;
    }
    
    /**
     * إنشاء حالة العنصر
     */
    private Map<String, Object> createItemState(TreeMenuItem item) {
        Map<String, Object> state = new HashMap<>();
        state.put("id", item.getId());
        state.put("expanded", item.isExpanded());
        state.put("favorite", item.isFavorite());
        state.put("accessCount", item.getAccessCount());
        state.put("sortOrder", item.getSortOrder());
        
        // حالة العناصر الفرعية
        if (!item.getChildren().isEmpty()) {
            List<Map<String, Object>> childrenState = new ArrayList<>();
            for (TreeMenuItem child : item.getChildren()) {
                childrenState.add(createItemState(child));
            }
            state.put("children", childrenState);
        }
        
        return state;
    }
    
    /**
     * تطبيق تكوين القائمة
     */
    private void applyMenuConfiguration(Map<String, Object> config) {
        try {
            // تطبيق حالة العناصر
            if (config.containsKey("itemsState")) {
                List<Map<String, Object>> itemsState = 
                    (List<Map<String, Object>>) config.get("itemsState");
                
                for (Map<String, Object> itemState : itemsState) {
                    applyItemState(itemState);
                }
            }
            
        } catch (Exception e) {
            logger.error("خطأ في تطبيق تكوين القائمة", e);
        }
    }
    
    /**
     * تطبيق حالة العنصر
     */
    private void applyItemState(Map<String, Object> state) {
        String id = (String) state.get("id");
        TreeMenuItem item = menuData.findItemById(id);
        
        if (item != null) {
            // تطبيق الخصائص
            if (state.containsKey("expanded")) {
                item.setExpanded((Boolean) state.get("expanded"));
            }
            if (state.containsKey("favorite")) {
                item.setFavorite((Boolean) state.get("favorite"));
            }
            if (state.containsKey("accessCount")) {
                item.setAccessCount((Integer) state.get("accessCount"));
            }
            if (state.containsKey("sortOrder")) {
                item.setSortOrder((Integer) state.get("sortOrder"));
            }
            
            // تطبيق حالة العناصر الفرعية
            if (state.containsKey("children")) {
                List<Map<String, Object>> childrenState = 
                    (List<Map<String, Object>>) state.get("children");
                
                for (Map<String, Object> childState : childrenState) {
                    applyItemState(childState);
                }
            }
        }
    }
    
    /**
     * الحصول على مسار ملف التكوين
     */
    private Path getConfigPath() {
        String userHome = System.getProperty("user.home");
        Path configDir = Paths.get(userHome, ".ship-erp", "config");
        
        try {
            Files.createDirectories(configDir);
        } catch (IOException e) {
            logger.error("خطأ في إنشاء مجلد التكوين", e);
        }
        
        return configDir.resolve(MENU_CONFIG_FILE);
    }
    
    /**
     * حفظ تفضيلات المستخدم
     */
    public void saveUserPreferences(String userId) {
        try {
            // حفظ العناصر المفضلة
            List<TreeMenuItem> favorites = menuData.getFavoriteItems();
            StringBuilder favoritesStr = new StringBuilder();
            for (TreeMenuItem item : favorites) {
                if (favoritesStr.length() > 0) {
                    favoritesStr.append(",");
                }
                favoritesStr.append(item.getId());
            }
            userPreferences.put("favorites_" + userId, favoritesStr.toString());
            
            // حفظ العناصر الموسعة
            StringBuilder expandedStr = new StringBuilder();
            for (TreeMenuItem item : menuData.getRootItems()) {
                collectExpandedItems(item, expandedStr);
            }
            userPreferences.put("expanded_" + userId, expandedStr.toString());
            
            userPreferences.flush();
            
            logger.debug("تم حفظ تفضيلات المستخدم: {}", userId);
            
        } catch (Exception e) {
            logger.error("خطأ في حفظ تفضيلات المستخدم", e);
        }
    }
    
    /**
     * تحميل تفضيلات المستخدم
     */
    public void loadUserPreferences(String userId) {
        try {
            // تحميل العناصر المفضلة
            String favoritesStr = userPreferences.get("favorites_" + userId, "");
            if (!favoritesStr.isEmpty()) {
                String[] favoriteIds = favoritesStr.split(",");
                for (String id : favoriteIds) {
                    TreeMenuItem item = menuData.findItemById(id.trim());
                    if (item != null) {
                        item.setFavorite(true);
                    }
                }
            }
            
            // تحميل العناصر الموسعة
            String expandedStr = userPreferences.get("expanded_" + userId, "");
            if (!expandedStr.isEmpty()) {
                String[] expandedIds = expandedStr.split(",");
                for (String id : expandedIds) {
                    TreeMenuItem item = menuData.findItemById(id.trim());
                    if (item != null) {
                        item.setExpanded(true);
                    }
                }
            }
            
            logger.debug("تم تحميل تفضيلات المستخدم: {}", userId);
            
        } catch (Exception e) {
            logger.error("خطأ في تحميل تفضيلات المستخدم", e);
        }
    }
    
    /**
     * جمع العناصر الموسعة
     */
    private void collectExpandedItems(TreeMenuItem item, StringBuilder expandedStr) {
        if (item.isExpanded()) {
            if (expandedStr.length() > 0) {
                expandedStr.append(",");
            }
            expandedStr.append(item.getId());
        }
        
        for (TreeMenuItem child : item.getChildren()) {
            collectExpandedItems(child, expandedStr);
        }
    }
    
    /**
     * إضافة عنصر مخصص للقائمة
     */
    public void addCustomMenuItem(TreeMenuItem parentItem, TreeMenuItem newItem) {
        try {
            if (parentItem != null) {
                parentItem.addChild(newItem);
            } else {
                menuData.addRootItem(newItem);
            }
            
            // حفظ التكوين المحدث
            saveMenuConfiguration();
            
            logger.info("تم إضافة عنصر مخصص: {}", newItem.getTitle());
            
        } catch (Exception e) {
            logger.error("خطأ في إضافة عنصر مخصص", e);
        }
    }
    
    /**
     * إزالة عنصر من القائمة
     */
    public void removeMenuItem(TreeMenuItem item) {
        try {
            TreeMenuItem parent = item.getParent();
            if (parent != null) {
                parent.removeChild(item);
            } else {
                menuData.removeRootItem(item);
            }
            
            // حفظ التكوين المحدث
            saveMenuConfiguration();
            
            logger.info("تم إزالة العنصر: {}", item.getTitle());
            
        } catch (Exception e) {
            logger.error("خطأ في إزالة العنصر", e);
        }
    }
    
    /**
     * إعادة ترتيب العناصر
     */
    public void reorderMenuItems(List<TreeMenuItem> items) {
        try {
            for (int i = 0; i < items.size(); i++) {
                items.get(i).setSortOrder(i + 1);
            }
            
            // حفظ التكوين المحدث
            saveMenuConfiguration();
            
            logger.info("تم إعادة ترتيب {} عنصر", items.size());
            
        } catch (Exception e) {
            logger.error("خطأ في إعادة ترتيب العناصر", e);
        }
    }
    
    /**
     * البحث في القائمة
     */
    public List<TreeMenuItem> searchMenu(String searchText) {
        return menuData.searchItems(searchText);
    }
    
    /**
     * الحصول على العناصر المفضلة
     */
    public List<TreeMenuItem> getFavoriteItems() {
        return menuData.getFavoriteItems();
    }
    
    /**
     * الحصول على العناصر الأكثر استخداماً
     */
    public List<TreeMenuItem> getMostUsedItems(int limit) {
        return menuData.getMostUsedItems(limit);
    }
    
    /**
     * إعادة تعيين إحصائيات الاستخدام
     */
    public void resetUsageStatistics() {
        menuData.resetAccessCounts();
        saveMenuConfiguration();
        logger.info("تم إعادة تعيين إحصائيات الاستخدام");
    }
    
    /**
     * تصدير تكوين القائمة
     */
    public void exportMenuConfiguration(File exportFile) throws IOException {
        Map<String, Object> config = createMenuConfiguration();
        objectMapper.writerWithDefaultPrettyPrinter()
                   .writeValue(exportFile, config);
        
        logger.info("تم تصدير تكوين القائمة إلى: {}", exportFile.getAbsolutePath());
    }
    
    /**
     * استيراد تكوين القائمة
     */
    public void importMenuConfiguration(File importFile) throws IOException {
        Map<String, Object> config = objectMapper.readValue(importFile, Map.class);
        applyMenuConfiguration(config);
        saveMenuConfiguration();
        
        logger.info("تم استيراد تكوين القائمة من: {}", importFile.getAbsolutePath());
    }
}
