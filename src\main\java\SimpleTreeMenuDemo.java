import com.shipment.erp.model.ui.TreeMenuItem;
import com.shipment.erp.model.ui.TreeMenuData;

import javax.swing.*;
import javax.swing.tree.*;
import java.awt.*;
import java.awt.event.*;
import java.util.List;
import java.util.Enumeration;

/**
 * تطبيق تجريبي مبسط للقائمة الشجرية
 * يعمل بدون تبعيات خارجية معقدة
 */
public class SimpleTreeMenuDemo extends JFrame {
    
    private TreeMenuData menuData;
    private JTree tree;
    private DefaultTreeModel treeModel;
    private DefaultMutableTreeNode rootNode;
    private JTextField searchField;
    private JPanel contentPanel;
    private JLabel statusLabel;
    
    public SimpleTreeMenuDemo() {
        initializeData();
        setupUI();
        setupEventHandlers();
        
        setTitle("تجربة القائمة الشجرية - Ship ERP");
        setSize(1000, 700);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        
        // تطبيق مظهر النظام
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            System.err.println("فشل في تطبيق مظهر النافذة: " + e.getMessage());
        }
    }
    
    /**
     * تهيئة البيانات
     */
    private void initializeData() {
        menuData = new TreeMenuData();
        
        // إضافة بيانات تجريبية إضافية
        addExtraMenuItems();
    }
    
    /**
     * إضافة عناصر قائمة إضافية
     */
    private void addExtraMenuItems() {
        // قسم المبيعات
        TreeMenuItem salesRoot = new TreeMenuItem("sales", "إدارة المبيعات", null);
        salesRoot.setDescription("إدارة عمليات البيع والعملاء");
        salesRoot.setActionType("CATEGORY");
        
        TreeMenuItem customers = new TreeMenuItem("sales.customers", "إدارة العملاء", null);
        customers.setDescription("إدارة بيانات العملاء");
        customers.setActionType("VIEW");
        customers.setFavorite(true);
        customers.setAccessCount(15);
        
        TreeMenuItem orders = new TreeMenuItem("sales.orders", "إدارة الطلبات", null);
        orders.setDescription("إدارة طلبات العملاء");
        orders.setActionType("VIEW");
        orders.setFavorite(true);
        orders.setAccessCount(12);
        
        salesRoot.addChild(customers);
        salesRoot.addChild(orders);
        
        // قسم التقارير المتقدمة
        TreeMenuItem advancedReports = new TreeMenuItem("reports.advanced", "التقارير المتقدمة", null);
        advancedReports.setDescription("تقارير تحليلية متقدمة");
        advancedReports.setActionType("CATEGORY");
        
        TreeMenuItem salesReports = new TreeMenuItem("reports.advanced.sales", "تقارير المبيعات", null);
        salesReports.setDescription("تحليل أداء المبيعات");
        salesReports.setActionType("REPORT");
        
        TreeMenuItem customerReports = new TreeMenuItem("reports.advanced.customers", "تقارير العملاء", null);
        customerReports.setDescription("تحليل سلوك العملاء");
        customerReports.setActionType("REPORT");
        
        advancedReports.addChild(salesReports);
        advancedReports.addChild(customerReports);
        
        // إضافة للقائمة الرئيسية
        menuData.addRootItem(salesRoot);
        
        // إضافة التقارير المتقدمة كفرع من التقارير الموجودة
        List<TreeMenuItem> rootItems = menuData.getRootItems();
        for (TreeMenuItem item : rootItems) {
            if ("reports".equals(item.getId())) {
                item.addChild(advancedReports);
                break;
            }
        }
    }
    
    /**
     * إعداد واجهة المستخدم
     */
    private void setupUI() {
        setLayout(new BorderLayout());
        
        // إنشاء الشجرة
        rootNode = new DefaultMutableTreeNode("القائمة الرئيسية");
        treeModel = new DefaultTreeModel(rootNode);
        tree = new JTree(treeModel);
        
        // إعداد الشجرة
        tree.setRootVisible(false);
        tree.setShowsRootHandles(true);
        tree.setCellRenderer(new CustomTreeCellRenderer());
        tree.setRowHeight(24);
        
        // تحميل البيانات
        loadTreeData();
        
        // شريط البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.add(new JLabel("البحث:"));
        searchField = new JTextField(20);
        searchPanel.add(searchField);
        
        JButton searchBtn = new JButton("بحث");
        searchBtn.addActionListener(e -> performSearch());
        searchPanel.add(searchBtn);
        
        JButton clearBtn = new JButton("مسح");
        clearBtn.addActionListener(e -> clearSearch());
        searchPanel.add(clearBtn);
        
        // لوحة المحتوى
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBorder(BorderFactory.createTitledBorder("منطقة المحتوى"));
        
        JLabel welcomeLabel = new JLabel("<html><div style='text-align: center;'>" +
                "<h1>مرحباً بك في نظام إدارة الشحنات</h1>" +
                "<p>اختر عنصراً من القائمة الجانبية لعرض محتواه</p>" +
                "<br><p><b>التعليمات:</b></p>" +
                "<ul>" +
                "<li>انقر مرة واحدة لتحديد العنصر</li>" +
                "<li>انقر مرتين لفتح العنصر</li>" +
                "<li>استخدم البحث للعثور على العناصر</li>" +
                "</ul>" +
                "</div></html>", SwingConstants.CENTER);
        
        contentPanel.add(welcomeLabel, BorderLayout.CENTER);
        
        // الفاصل الرئيسي
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        
        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.add(searchPanel, BorderLayout.NORTH);
        leftPanel.add(new JScrollPane(tree), BorderLayout.CENTER);
        leftPanel.setPreferredSize(new Dimension(350, 600));
        
        splitPane.setLeftComponent(leftPanel);
        splitPane.setRightComponent(contentPanel);
        splitPane.setDividerLocation(350);
        splitPane.setResizeWeight(0.0);
        
        // شريط الحالة
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusLabel = new JLabel("جاهز - اختر عنصراً من القائمة");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        // شريط أدوات
        JToolBar toolBar = new JToolBar();
        toolBar.setFloatable(false);
        
        JButton expandAllBtn = new JButton("توسيع الكل");
        expandAllBtn.addActionListener(e -> expandAll());
        
        JButton collapseAllBtn = new JButton("طي الكل");
        collapseAllBtn.addActionListener(e -> collapseAll());
        
        JButton favoritesBtn = new JButton("المفضلة");
        favoritesBtn.addActionListener(e -> showFavorites());
        
        toolBar.add(expandAllBtn);
        toolBar.add(collapseAllBtn);
        toolBar.addSeparator();
        toolBar.add(favoritesBtn);
        
        add(toolBar, BorderLayout.NORTH);
        add(splitPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * تحميل بيانات الشجرة
     */
    private void loadTreeData() {
        rootNode.removeAllChildren();
        
        for (TreeMenuItem rootItem : menuData.getRootItems()) {
            DefaultMutableTreeNode node = createTreeNode(rootItem);
            rootNode.add(node);
        }
        
        treeModel.reload();
        expandDefaultNodes();
    }
    
    /**
     * إنشاء عقدة شجرية
     */
    private DefaultMutableTreeNode createTreeNode(TreeMenuItem item) {
        DefaultMutableTreeNode node = new DefaultMutableTreeNode(item);
        
        for (TreeMenuItem child : item.getChildren()) {
            DefaultMutableTreeNode childNode = createTreeNode(child);
            node.add(childNode);
        }
        
        return node;
    }
    
    /**
     * توسيع العقد الافتراضية
     */
    private void expandDefaultNodes() {
        for (int i = 0; i < tree.getRowCount(); i++) {
            tree.expandRow(i);
        }
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    private void setupEventHandlers() {
        // مستمع تحديد العقد
        tree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
            if (selectedNode != null && selectedNode.getUserObject() instanceof TreeMenuItem) {
                TreeMenuItem item = (TreeMenuItem) selectedNode.getUserObject();
                showItemInfo(item);
                updateStatus("تم تحديد: " + item.getTitle());
            }
        });
        
        // مستمع النقر المزدوج
        tree.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
                    if (selectedNode != null && selectedNode.getUserObject() instanceof TreeMenuItem) {
                        TreeMenuItem item = (TreeMenuItem) selectedNode.getUserObject();
                        openItem(item);
                    }
                }
            }
        });
        
        // مستمع البحث
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    performSearch();
                }
            }
        });
    }
    
    /**
     * عرض معلومات العنصر
     */
    private void showItemInfo(TreeMenuItem item) {
        contentPanel.removeAll();
        
        JPanel infoPanel = new JPanel(new BorderLayout());
        infoPanel.setBorder(BorderFactory.createTitledBorder("معلومات العنصر"));
        
        String info = String.format("<html><div style='padding: 20px;'>" +
                "<h2>%s</h2>" +
                "<p><b>المعرف:</b> %s</p>" +
                "<p><b>الوصف:</b> %s</p>" +
                "<p><b>النوع:</b> %s</p>" +
                "<p><b>المسار الكامل:</b> %s</p>" +
                "<p><b>عدد مرات الوصول:</b> %d</p>" +
                "<p><b>مفضل:</b> %s</p>" +
                "</div></html>",
                item.getTitle(),
                item.getId(),
                item.getDescription() != null ? item.getDescription() : "غير محدد",
                item.getActionType() != null ? item.getActionType() : "غير محدد",
                item.getFullPath(),
                item.getAccessCount(),
                item.isFavorite() ? "نعم" : "لا");
        
        JLabel infoLabel = new JLabel(info);
        infoPanel.add(infoLabel, BorderLayout.CENTER);
        
        contentPanel.add(infoPanel, BorderLayout.CENTER);
        contentPanel.revalidate();
        contentPanel.repaint();
    }
    
    /**
     * فتح العنصر
     */
    private void openItem(TreeMenuItem item) {
        item.incrementAccessCount();
        updateStatus("تم فتح: " + item.getTitle());
        
        JOptionPane.showMessageDialog(this,
                "تم فتح العنصر: " + item.getTitle() + "\n" +
                "النوع: " + item.getActionType() + "\n" +
                "عدد مرات الوصول: " + item.getAccessCount(),
                "فتح العنصر",
                JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * تنفيذ البحث
     */
    private void performSearch() {
        String searchText = searchField.getText().trim();
        
        if (searchText.isEmpty()) {
            loadTreeData();
            return;
        }
        
        List<TreeMenuItem> results = menuData.searchItems(searchText);
        
        rootNode.removeAllChildren();
        
        if (!results.isEmpty()) {
            DefaultMutableTreeNode searchNode = new DefaultMutableTreeNode("نتائج البحث");
            
            for (TreeMenuItem item : results) {
                DefaultMutableTreeNode resultNode = new DefaultMutableTreeNode(item);
                searchNode.add(resultNode);
            }
            
            rootNode.add(searchNode);
            tree.expandPath(new TreePath(new Object[]{rootNode, searchNode}));
        }
        
        treeModel.reload();
        updateStatus("تم العثور على " + results.size() + " نتيجة");
    }
    
    /**
     * مسح البحث
     */
    private void clearSearch() {
        searchField.setText("");
        loadTreeData();
        updateStatus("تم مسح البحث");
    }
    
    /**
     * توسيع جميع العقد
     */
    private void expandAll() {
        for (int i = 0; i < tree.getRowCount(); i++) {
            tree.expandRow(i);
        }
        updateStatus("تم توسيع جميع العقد");
    }
    
    /**
     * طي جميع العقد
     */
    private void collapseAll() {
        for (int i = tree.getRowCount() - 1; i >= 0; i--) {
            tree.collapseRow(i);
        }
        updateStatus("تم طي جميع العقد");
    }
    
    /**
     * عرض المفضلة
     */
    private void showFavorites() {
        List<TreeMenuItem> favorites = menuData.getFavoriteItems();
        StringBuilder sb = new StringBuilder("العناصر المفضلة:\n\n");
        
        if (favorites.isEmpty()) {
            sb.append("لا توجد عناصر مفضلة");
        } else {
            for (TreeMenuItem item : favorites) {
                sb.append("• ").append(item.getTitle())
                  .append(" (").append(item.getAccessCount()).append(" مرة)")
                  .append("\n");
            }
        }
        
        JOptionPane.showMessageDialog(this, sb.toString(), "العناصر المفضلة", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * تحديث شريط الحالة
     */
    private void updateStatus(String message) {
        statusLabel.setText(message);
    }
    
    /**
     * مصيّر خلايا الشجرة المخصص
     */
    private class CustomTreeCellRenderer extends DefaultTreeCellRenderer {
        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value,
                boolean selected, boolean expanded, boolean leaf, int row, boolean hasFocus) {
            
            super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row, hasFocus);
            
            if (value instanceof DefaultMutableTreeNode) {
                DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
                Object userObject = node.getUserObject();
                
                if (userObject instanceof TreeMenuItem) {
                    TreeMenuItem item = (TreeMenuItem) userObject;
                    setText(item.getTitle());
                    
                    if (item.isFavorite()) {
                        setForeground(new Color(0, 100, 200));
                    }
                    
                    if (item.getDescription() != null) {
                        setToolTipText(item.getDescription());
                    }
                }
            }
            
            return this;
        }
    }
    
    /**
     * تشغيل التطبيق
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new SimpleTreeMenuDemo().setVisible(true);
        });
    }
}
