@echo off
chcp 65001 >nul
echo ========================================
echo تجميع وتشغيل القائمة الشجرية المتقدمة
echo Ship ERP - Advanced Tree Menu
echo ========================================
echo.

REM التحقق من وجود Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Java غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Java 8 أو أحدث من:
    echo https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo ✅ تم العثور على Java
echo.

REM إنشاء مجلدات الكلاسات
echo 📁 إنشاء مجلدات الكلاسات...
if not exist "target" mkdir "target"
if not exist "target\classes" mkdir "target\classes"
if not exist "target\classes\com" mkdir "target\classes\com"
if not exist "target\classes\com\shipment" mkdir "target\classes\com\shipment"
if not exist "target\classes\com\shipment\erp" mkdir "target\classes\com\shipment\erp"

echo.
echo 🔨 بدء التجميع...
echo.

REM تجميع نماذج البيانات
echo تجميع نماذج البيانات...
javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "src\main\java\com\shipment\erp\model\ui\TreeMenuItem.java"
if %errorlevel% neq 0 (
    echo ❌ خطأ في تجميع TreeMenuItem.java
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "src\main\java\com\shipment\erp\model\ui\TreeMenuData.java"
if %errorlevel% neq 0 (
    echo ❌ خطأ في تجميع TreeMenuData.java
    pause
    exit /b 1
)

REM إنشاء ملف خدمة مبسط
echo إنشاء خدمة مبسطة...
echo package com.shipment.erp.service; > "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo import com.shipment.erp.model.ui.TreeMenuData; >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo public class TreeMenuService { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     private TreeMenuData menuData; >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     public TreeMenuService() { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo         this.menuData = new TreeMenuData(); >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     public TreeMenuData getMenuData() { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo         return menuData; >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     public void updateMenuPermissions() { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo         // تحديث الصلاحيات >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     public void saveMenuConfiguration() { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo         // حفظ الإعدادات >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"

javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "target\classes\com\shipment\erp\service\TreeMenuService.java"
if %errorlevel% neq 0 (
    echo ❌ خطأ في تجميع TreeMenuService.java
    pause
    exit /b 1
)

REM تجميع مكونات الواجهة
echo تجميع مكونات الواجهة...
javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "src\main\java\com\shipment\erp\ui\components\AdvancedTreeMenu.java"
if %errorlevel% neq 0 (
    echo ❌ خطأ في تجميع AdvancedTreeMenu.java
    pause
    exit /b 1
)

REM تجميع التطبيق التجريبي المبسط
echo تجميع التطبيق التجريبي المبسط...
javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "src\main\java\SimpleTreeMenuDemo.java"
if %errorlevel% neq 0 (
    echo ❌ خطأ في تجميع SimpleTreeMenuDemo.java
    pause
    exit /b 1
)

echo.
echo ✅ تم التجميع بنجاح!
echo.

echo 🚀 تشغيل التطبيق...
echo.

REM تشغيل التطبيق
java -Dfile.encoding=UTF-8 -cp "target\classes" SimpleTreeMenuDemo

echo.
echo 🏁 انتهى التطبيق
echo.
pause
