# 🚀 دليل تثبيت Java وتشغيل القائمة الشجرية

## ⚠️ Java غير مثبت على النظام

لتشغيل القائمة الشجرية المتقدمة، تحتاج إلى تثبيت Java أولاً.

## 📥 تحميل وتثبيت Java

### الخطوة 1: تحميل Java
1. اذهب إلى الموقع الرسمي: **https://www.oracle.com/java/technologies/downloads/**
2. اختر **Java 8** أو **Java 11** أو **Java 17** (الأحدث)
3. اختر النسخة المناسبة لنظام التشغيل:
   - **Windows x64 Installer** (ملف .exe)

### الخطوة 2: تثبيت Java
1. شغل ملف التثبيت المحمل
2. اتب<PERSON> خطوات التثبيت (Next > Next > Install)
3. انتظر حتى انتهاء التثبيت
4. اضغط **Close** عند الانتهاء

### الخطوة 3: التحقق من التثبيت
افتح Command Prompt أو PowerShell واكتب:
```cmd
java -version
```

يجب أن تظهر رسالة مثل:
```
java version "1.8.0_XXX"
Java(TM) SE Runtime Environment (build 1.8.0_XXX-bXX)
Java HotSpot(TM) 64-Bit Server VM (build XX.XXX-bXX, mixed mode)
```

## 🏃‍♂️ تشغيل التطبيق بعد تثبيت Java

### الطريقة الأولى - التشغيل السريع:
```cmd
cd d:\java
.\run-simple.bat
```

### الطريقة الثانية - التجميع اليدوي:
```cmd
cd d:\java

# إنشاء مجلد الكلاسات
mkdir target\classes

# تجميع الملفات
javac -encoding UTF-8 -cp target\classes -d target\classes src\main\java\com\shipment\erp\model\ui\TreeMenuItem.java
javac -encoding UTF-8 -cp target\classes -d target\classes src\main\java\com\shipment\erp\model\ui\TreeMenuData.java
javac -encoding UTF-8 -cp target\classes -d target\classes src\main\java\SimpleTreeMenuDemo.java

# تشغيل التطبيق
java -Dfile.encoding=UTF-8 -cp target\classes SimpleTreeMenuDemo
```

## 🎯 ما ستراه عند التشغيل

### النافذة الرئيسية:
- **القائمة الشجرية** على اليسار مع جميع الأقسام
- **منطقة المحتوى** على اليمين لعرض التفاصيل
- **شريط البحث** في الأعلى
- **شريط الأدوات** مع أزرار التحكم
- **شريط الحالة** في الأسفل

### الأقسام المتاحة:
- 🔧 **نظام الإعدادات**
  - المتغيرات العامة
  - السنة المالية
  - إدارة العملات
  - بيانات الشركة
  - إدارة المستخدمين

- 📦 **إدارة الأصناف**
  - تصنيفات الأصناف
  - قائمة الأصناف

- 🏢 **إدارة الموردين**
  - قائمة الموردين

- 🚚 **إدارة الشحنات**
  - قائمة الشحنات
  - تتبع الشحنات

- 💰 **إدارة المبيعات** ⭐
  - إدارة العملاء ⭐
  - إدارة الطلبات ⭐
  - الفواتير

- 🛒 **إدارة المشتريات**
  - أوامر الشراء
  - إشعارات الاستلام

- 📊 **المحاسبة**
  - دليل الحسابات
  - القيود اليومية
  - ميزان المراجعة

- 📈 **التقارير**
  - التقارير المالية
  - تقارير المخزون
  - التقارير المتقدمة

## 🎮 كيفية الاستخدام

### التنقل:
- **نقرة واحدة**: تحديد العنصر وعرض معلوماته
- **نقرة مزدوجة**: فتح العنصر وتنفيذ إجراءه

### البحث:
1. اكتب في مربع البحث
2. النتائج تظهر فورياً
3. اضغط Enter للبحث
4. استخدم زر "مسح" لإعادة تحميل القائمة

### شريط الأدوات:
- **توسيع الكل**: توسيع جميع العقد
- **طي الكل**: طي جميع العقد
- **المفضلة**: عرض العناصر المفضلة (⭐)

## 🔧 استكشاف الأخطاء

### مشكلة: "java is not recognized"
**السبب**: Java غير مثبت أو غير موجود في PATH
**الحل**: تثبيت Java من الرابط أعلاه

### مشكلة: "Error compiling"
**السبب**: مشكلة في ملفات الكود
**الحل**: تأكد من وجود جميع الملفات في المجلدات الصحيحة

### مشكلة: النص العربي لا يظهر
**السبب**: مشكلة في الترميز
**الحل**: استخدم `-Dfile.encoding=UTF-8` عند التشغيل

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Java بشكل صحيح
2. تحقق من وجود جميع الملفات
3. راجع `TREE_MENU_GUIDE.md` للتفاصيل الكاملة

---

## 🎉 مبروك!

بعد تثبيت Java، ستتمكن من تشغيل القائمة الشجرية المتقدمة والاستمتاع بجميع ميزاتها:

- ✅ عرض شجري منظم
- ✅ بحث متقدم
- ✅ نظام مفضلة
- ✅ واجهة سهلة الاستخدام
- ✅ دعم اللغة العربية

**القائمة الشجرية جاهزة وتنتظر تثبيت Java فقط!** 🚀
