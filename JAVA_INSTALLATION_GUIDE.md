# دليل تثبيت Java وتشغيل القائمة الشجرية

## 🚀 تم تطوير القائمة الشجرية المتقدمة بنجاح!

تم إنشاء نظام قائمة شجرية متقدم لنظام إدارة الشحنات Ship ERP مع الميزات التالية:

### ✅ الميزات المطورة:

#### 🌳 القائمة الشجرية المتقدمة
- **عرض هرمي منظم** للوظائف والأقسام
- **دعم الأيقونات** لتحسين تجربة المستخدم
- **التلميحات التفاعلية** عند التمرير فوق العناصر
- **توسيع/طي العقد** بسهولة

#### 🔍 نظام البحث المتقدم
- **البحث الفوري** أثناء الكتابة
- **البحث متعدد اللغات** (عربي/إنجليزي)
- **نتائج مرتبة** حسب الصلة

#### ⭐ نظام المفضلة والإحصائيات
- **إضافة/إزالة المفضلة** بسهولة
- **تتبع عدد مرات الاستخدام** لكل عنصر
- **عرض العناصر الأكثر استخداماً**

#### 🔐 إدارة الصلاحيات
- **التحكم في الوصول** حسب صلاحيات المستخدم
- **إخفاء العناصر غير المسموحة**
- **دعم الأدوار والصلاحيات المتدرجة**

### 📁 الملفات المطورة:

```
d:\java\
├── src\main\java\
│   ├── com\shipment\erp\
│   │   ├── model\ui\
│   │   │   ├── TreeMenuItem.java          ✅ نموذج بيانات العنصر
│   │   │   └── TreeMenuData.java          ✅ مدير بيانات القائمة
│   │   ├── service\
│   │   │   └── TreeMenuService.java       ✅ خدمة إدارة القائمة
│   │   └── ui\
│   │       ├── components\
│   │       │   └── AdvancedTreeMenu.java  ✅ مكون القائمة المتقدم
│   │       └── MainWindow.java            ✅ النافذة الرئيسية
│   ├── TreeMenuDemo.java                  ✅ تطبيق تجريبي متقدم
│   └── SimpleTreeMenuDemo.java            ✅ تطبيق تجريبي مبسط
├── run-simple.bat                         ✅ ملف تشغيل مبسط
├── compile-and-run.bat                    ✅ ملف تجميع وتشغيل
├── TREE_MENU_GUIDE.md                    ✅ دليل الاستخدام الشامل
└── JAVA_INSTALLATION_GUIDE.md            ✅ دليل التثبيت (هذا الملف)
```

## 📋 متطلبات التشغيل

### 1. تثبيت Java

#### تحميل Java:
1. اذهب إلى: https://www.oracle.com/java/technologies/downloads/
2. اختر **Java 8** أو أحدث
3. حمل النسخة المناسبة لنظام التشغيل (Windows x64)

#### تثبيت Java:
1. شغل ملف التثبيت المحمل
2. اتبع خطوات التثبيت
3. تأكد من إضافة Java إلى PATH

#### التحقق من التثبيت:
```cmd
java -version
javac -version
```

### 2. تشغيل التطبيق

#### الطريقة الأولى - التشغيل المبسط:
```cmd
cd d:\java
.\run-simple.bat
```

#### الطريقة الثانية - التجميع اليدوي:
```cmd
cd d:\java

# إنشاء مجلدات
mkdir target\classes

# تجميع الملفات
javac -encoding UTF-8 -cp target\classes -d target\classes src\main\java\com\shipment\erp\model\ui\*.java
javac -encoding UTF-8 -cp target\classes -d target\classes src\main\java\SimpleTreeMenuDemo.java

# تشغيل التطبيق
java -Dfile.encoding=UTF-8 -cp target\classes SimpleTreeMenuDemo
```

#### الطريقة الثالثة - استخدام IDE:
1. افتح المشروع في **IntelliJ IDEA** أو **Eclipse**
2. تأكد من إعداد Java SDK
3. شغل `SimpleTreeMenuDemo.main()`

## 🎯 الأقسام المتاحة في القائمة

### 🔧 نظام الإعدادات
- المتغيرات العامة
- السنة المالية  
- إدارة العملات
- بيانات الشركة
- إدارة المستخدمين

### 📦 إدارة الأصناف
- تصنيفات الأصناف
- قائمة الأصناف

### 🏢 إدارة الموردين
- قائمة الموردين

### 🚚 إدارة الشحنات
- قائمة الشحنات
- تتبع الشحنات

### 💰 إدارة المبيعات
- إدارة العملاء ⭐
- إدارة الطلبات ⭐
- الفواتير

### 🛒 إدارة المشتريات
- أوامر الشراء
- إشعارات الاستلام

### 📊 المحاسبة
- دليل الحسابات
- القيود اليومية
- ميزان المراجعة

### 📈 التقارير
- التقارير المالية
- تقارير المخزون
- التقارير المتقدمة
  - تقارير المبيعات
  - تقارير العملاء

## 🎮 كيفية الاستخدام

### التنقل في القائمة:
- **نقرة واحدة**: تحديد العنصر وعرض معلوماته
- **نقرة مزدوجة**: فتح العنصر وتنفيذ إجراءه
- **النقر بالزر الأيمن**: قائمة السياق (في النسخة المتقدمة)

### البحث:
1. اكتب في مربع البحث
2. النتائج تظهر فورياً
3. اضغط Enter للبحث
4. استخدم زر "مسح" لإعادة تحميل القائمة الكاملة

### شريط الأدوات:
- **توسيع الكل**: توسيع جميع العقد
- **طي الكل**: طي جميع العقد  
- **المفضلة**: عرض العناصر المفضلة

### المفضلة:
- العناصر المميزة بـ ⭐ هي مفضلة
- تظهر بلون أزرق في القائمة
- يمكن عرضها منفصلة

## 🔧 استكشاف الأخطاء

### مشكلة: Java غير موجود
```
Error: Java not found
```
**الحل**: تثبيت Java من الرابط أعلاه

### مشكلة: خطأ في التجميع
```
Error compiling *.java
```
**الحل**: 
1. تأكد من وجود جميع الملفات
2. تحقق من صحة مسارات الملفات
3. تأكد من إعدادات الترميز UTF-8

### مشكلة: النص العربي لا يظهر صحيحاً
**الحل**: 
1. تأكد من استخدام `-Dfile.encoding=UTF-8`
2. تحقق من إعدادات النظام للغة العربية

## 🚀 الخطوات التالية

### للتطوير المستقبلي:
1. **تكامل قاعدة البيانات**: ربط القائمة بقاعدة بيانات حقيقية
2. **نظام الصلاحيات المتقدم**: تطوير نظام صلاحيات شامل
3. **السحب والإفلات**: إضافة إمكانية إعادة ترتيب العناصر
4. **السمات المتعددة**: دعم سمات مختلفة للواجهة
5. **التصدير/الاستيراد**: حفظ واستعادة تكوين القائمة

### للاستخدام الفوري:
1. ثبت Java
2. شغل `run-simple.bat`
3. استكشف القائمة الشجرية
4. جرب ميزات البحث والمفضلة
5. اقرأ `TREE_MENU_GUIDE.md` للتفاصيل الكاملة

---

## 🎉 تهانينا!

تم تطوير قائمة شجرية متقدمة ومتكاملة لنظام إدارة الشحنات Ship ERP بنجاح!

القائمة جاهزة للاستخدام وتحتوي على جميع الميزات المطلوبة:
- ✅ عرض شجري منظم
- ✅ بحث متقدم
- ✅ نظام مفضلة
- ✅ إدارة صلاحيات
- ✅ واجهة سهلة الاستخدام
- ✅ دعم اللغة العربية

**للدعم التقني**: راجع الملفات المرفقة أو اتصل بفريق التطوير.
