package com.shipment.erp.model.ui;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * مدير بيانات القائمة الشجرية
 * يحتوي على جميع عناصر القائمة ويوفر وظائف البحث والفلترة
 */
public class TreeMenuData {

    private static final Logger logger = LoggerFactory.getLogger(TreeMenuData.class);

    private final List<TreeMenuItem> rootItems = new ArrayList<>();
    private final Map<String, TreeMenuItem> itemsMap = new HashMap<>();
    private final Map<String, List<TreeMenuItem>> categoriesMap = new HashMap<>();

    /**
     * منشئ افتراضي
     */
    public TreeMenuData() {
        initializeDefaultMenu();
    }

    /**
     * تهيئة القائمة الافتراضية
     */
    private void initializeDefaultMenu() {
        // نظام الإعدادات
        TreeMenuItem settingsRoot =
                new TreeMenuItem("settings", "نظام الإعدادات", "System Settings",
                        "إعدادات النظام العامة", "/images/settings.png", "CATEGORY", null);
        settingsRoot.setCategory("SYSTEM");
        settingsRoot.setSortOrder(1);

        // الإعدادات العامة
        TreeMenuItem generalSettings = new TreeMenuItem("settings.general", "المتغيرات العامة",
                "General Settings", "إعدادات النظام العامة", "/images/general.png", "VIEW",
                "/fxml/settings/general-settings.fxml");
        generalSettings.setRequiredPermission("SETTINGS_GENERAL");
        generalSettings.setSortOrder(1);

        // السنة المالية
        TreeMenuItem fiscalYear = new TreeMenuItem("settings.fiscal", "السنة المالية",
                "Fiscal Year", "إدارة السنوات المالية", "/images/calendar.png", "VIEW",
                "/fxml/settings/fiscal-year-settings.fxml");
        fiscalYear.setRequiredPermission("SETTINGS_FISCAL");
        fiscalYear.setSortOrder(2);

        // العملات
        TreeMenuItem currencies = new TreeMenuItem("settings.currencies", "إدارة العملات",
                "Currencies", "إدارة العملات وأسعار الصرف", "/images/currency.png", "VIEW",
                "/fxml/settings/currencies-settings.fxml");
        currencies.setRequiredPermission("SETTINGS_CURRENCIES");
        currencies.setSortOrder(3);

        // بيانات الشركة
        TreeMenuItem company = new TreeMenuItem("settings.company", "بيانات الشركة", "Company Data",
                "إدارة بيانات الشركة والفروع", "/images/company.png", "VIEW",
                "/fxml/settings/company-settings.fxml");
        company.setRequiredPermission("SETTINGS_COMPANY");
        company.setSortOrder(4);

        // إدارة المستخدمين
        TreeMenuItem users = new TreeMenuItem("settings.users", "إدارة المستخدمين",
                "User Management", "إدارة المستخدمين والصلاحيات", "/images/users.png", "VIEW",
                "/fxml/settings/user-management.fxml");
        users.setRequiredPermission("SETTINGS_USERS");
        users.setSortOrder(5);

        // إضافة العناصر الفرعية للإعدادات
        settingsRoot.addChild(generalSettings);
        settingsRoot.addChild(fiscalYear);
        settingsRoot.addChild(currencies);
        settingsRoot.addChild(company);
        settingsRoot.addChild(users);

        // إدارة الأصناف
        TreeMenuItem itemsRoot = new TreeMenuItem("items", "إدارة الأصناف", "Items Management",
                "إدارة الأصناف والمنتجات", "/images/items.png", "CATEGORY", null);
        itemsRoot.setCategory("INVENTORY");
        itemsRoot.setSortOrder(2);

        TreeMenuItem itemCategories = new TreeMenuItem("items.categories", "تصنيفات الأصناف",
                "Item Categories", "إدارة تصنيفات الأصناف", "/images/categories.png", "VIEW",
                "/fxml/items/categories.fxml");
        itemCategories.setRequiredPermission("ITEMS_CATEGORIES");

        TreeMenuItem itemsList = new TreeMenuItem("items.list", "قائمة الأصناف", "Items List",
                "عرض وإدارة قائمة الأصناف", "/images/list.png", "VIEW",
                "/fxml/items/items-list.fxml");
        itemsList.setRequiredPermission("ITEMS_VIEW");

        itemsRoot.addChild(itemCategories);
        itemsRoot.addChild(itemsList);

        // إدارة الموردين
        TreeMenuItem suppliersRoot = new TreeMenuItem("suppliers", "إدارة الموردين",
                "Suppliers Management", "إدارة الموردين والعلاقات التجارية",
                "/images/suppliers.png", "CATEGORY", null);
        suppliersRoot.setCategory("SUPPLIERS");
        suppliersRoot.setSortOrder(3);

        TreeMenuItem suppliersList = new TreeMenuItem("suppliers.list", "قائمة الموردين",
                "Suppliers List", "عرض وإدارة قائمة الموردين", "/images/supplier-list.png", "VIEW",
                "/fxml/suppliers/suppliers-list.fxml");
        suppliersList.setRequiredPermission("SUPPLIERS_VIEW");

        suppliersRoot.addChild(suppliersList);

        // إدارة الشحنات
        TreeMenuItem shipmentsRoot =
                new TreeMenuItem("shipments", "إدارة الشحنات", "Shipments Management",
                        "متابعة وإدارة الشحنات", "/images/shipments.png", "CATEGORY", null);
        shipmentsRoot.setCategory("SHIPMENTS");
        shipmentsRoot.setSortOrder(4);

        TreeMenuItem shipmentsList = new TreeMenuItem("shipments.list", "قائمة الشحنات",
                "Shipments List", "عرض ومتابعة الشحنات", "/images/shipment-list.png", "VIEW",
                "/fxml/shipments/shipments-list.fxml");
        shipmentsList.setRequiredPermission("SHIPMENTS_VIEW");

        TreeMenuItem shipmentsTracking = new TreeMenuItem("shipments.tracking", "تتبع الشحنات",
                "Shipments Tracking", "تتبع حالة الشحنات", "/images/tracking.png", "VIEW",
                "/fxml/shipments/tracking.fxml");
        shipmentsTracking.setRequiredPermission("SHIPMENTS_TRACKING");

        shipmentsRoot.addChild(shipmentsList);
        shipmentsRoot.addChild(shipmentsTracking);

        // التقارير
        TreeMenuItem reportsRoot = new TreeMenuItem("reports", "التقارير", "Reports",
                "تقارير النظام المختلفة", "/images/reports.png", "CATEGORY", null);
        reportsRoot.setCategory("REPORTS");
        reportsRoot.setSortOrder(5);

        TreeMenuItem financialReports = new TreeMenuItem("reports.financial", "التقارير المالية",
                "Financial Reports", "التقارير المالية والمحاسبية", "/images/financial.png", "VIEW",
                "/fxml/reports/financial.fxml");
        financialReports.setRequiredPermission("REPORTS_FINANCIAL");

        TreeMenuItem inventoryReports = new TreeMenuItem("reports.inventory", "تقارير المخزون",
                "Inventory Reports", "تقارير المخزون والأصناف", "/images/inventory.png", "VIEW",
                "/fxml/reports/inventory.fxml");
        inventoryReports.setRequiredPermission("REPORTS_INVENTORY");

        reportsRoot.addChild(financialReports);
        reportsRoot.addChild(inventoryReports);

        // إضافة العناصر الجذرية
        addRootItem(settingsRoot);
        addRootItem(itemsRoot);
        addRootItem(suppliersRoot);
        addRootItem(shipmentsRoot);
        addRootItem(reportsRoot);

        logger.info("تم تهيئة القائمة الافتراضية بنجاح");
    }

    /**
     * إضافة عنصر جذري
     */
    public void addRootItem(TreeMenuItem item) {
        rootItems.add(item);
        addToMaps(item);
        sortRootItems();
    }

    /**
     * إزالة عنصر جذري
     */
    public void removeRootItem(TreeMenuItem item) {
        rootItems.remove(item);
        removeFromMaps(item);
    }

    /**
     * إضافة العنصر إلى الخرائط
     */
    private void addToMaps(TreeMenuItem item) {
        itemsMap.put(item.getId(), item);

        String category = item.getCategory();
        if (category != null && !category.isEmpty()) {
            categoriesMap.computeIfAbsent(category, k -> new ArrayList<>()).add(item);
        }

        // إضافة العناصر الفرعية
        for (TreeMenuItem child : item.getChildren()) {
            addToMaps(child);
        }
    }

    /**
     * إزالة العنصر من الخرائط
     */
    private void removeFromMaps(TreeMenuItem item) {
        itemsMap.remove(item.getId());

        String category = item.getCategory();
        if (category != null && categoriesMap.containsKey(category)) {
            categoriesMap.get(category).remove(item);
        }

        // إزالة العناصر الفرعية
        for (TreeMenuItem child : item.getChildren()) {
            removeFromMaps(child);
        }
    }

    /**
     * ترتيب العناصر الجذرية
     */
    private void sortRootItems() {
        rootItems.sort(Comparator.comparingInt(TreeMenuItem::getSortOrder));
    }

    /**
     * الحصول على العناصر الجذرية
     */
    public List<TreeMenuItem> getRootItems() {
        return rootItems;
    }

    /**
     * البحث عن عنصر بالمعرف
     */
    public TreeMenuItem findItemById(String id) {
        return itemsMap.get(id);
    }

    /**
     * البحث في النصوص
     */
    public List<TreeMenuItem> searchItems(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return new ArrayList<>();
        }

        String lowerSearchText = searchText.toLowerCase();
        return itemsMap.values().stream()
                .filter(item -> item.getTitle().toLowerCase().contains(lowerSearchText)
                        || (item.getTitleEn() != null
                                && item.getTitleEn().toLowerCase().contains(lowerSearchText))
                        || (item.getDescription() != null
                                && item.getDescription().toLowerCase().contains(lowerSearchText)))
                .collect(Collectors.toList());
    }

    /**
     * الفلترة حسب الفئة
     */
    public List<TreeMenuItem> getItemsByCategory(String category) {
        return categoriesMap.getOrDefault(category, new ArrayList<>());
    }

    /**
     * الحصول على العناصر المفضلة
     */
    public List<TreeMenuItem> getFavoriteItems() {
        return itemsMap.values().stream().filter(TreeMenuItem::isFavorite)
                .sorted(Comparator.comparingInt(TreeMenuItem::getAccessCount).reversed())
                .collect(Collectors.toList());
    }

    /**
     * الحصول على العناصر الأكثر استخداماً
     */
    public List<TreeMenuItem> getMostUsedItems(int limit) {
        return itemsMap.values().stream().filter(item -> item.getAccessCount() > 0)
                .sorted(Comparator.comparingInt(TreeMenuItem::getAccessCount).reversed())
                .limit(limit).collect(Collectors.toList());
    }

    /**
     * الحصول على جميع الفئات
     */
    public Set<String> getAllCategories() {
        return categoriesMap.keySet();
    }

    /**
     * تحديث صلاحيات العناصر
     */
    public void updatePermissions(Set<String> userPermissions, Set<String> userRoles) {
        for (TreeMenuItem item : itemsMap.values()) {
            boolean hasPermission = true;

            // التحقق من الصلاحية المطلوبة
            if (item.getRequiredPermission() != null && !item.getRequiredPermission().isEmpty()) {
                hasPermission = userPermissions.contains(item.getRequiredPermission());
            }

            // التحقق من الدور المطلوب
            if (hasPermission && item.getRequiredRole() != null
                    && !item.getRequiredRole().isEmpty()) {
                hasPermission = userRoles.contains(item.getRequiredRole());
            }

            item.setHasPermission(hasPermission);
        }
    }

    /**
     * الحصول على العناصر المتاحة للمستخدم
     */
    public List<TreeMenuItem> getAccessibleItems() {
        return itemsMap.values().stream().filter(TreeMenuItem::canAccess)
                .collect(Collectors.toList());
    }

    /**
     * إعادة تعيين عدادات الوصول
     */
    public void resetAccessCounts() {
        itemsMap.values().forEach(item -> item.setAccessCount(0));
    }

    /**
     * تصدير بيانات القائمة
     */
    public Map<String, Object> exportData() {
        Map<String, Object> data = new HashMap<>();
        data.put("rootItems", rootItems);
        data.put("totalItems", itemsMap.size());
        data.put("categories", getAllCategories());
        return data;
    }

    /**
     * مسح جميع البيانات
     */
    public void clear() {
        rootItems.clear();
        itemsMap.clear();
        categoriesMap.clear();
    }
}
