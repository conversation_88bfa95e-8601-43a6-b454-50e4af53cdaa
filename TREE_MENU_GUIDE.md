# دليل القائمة الشجرية المتقدمة - Ship ERP

## نظرة عامة

تم تطوير قائمة شجرية متقدمة لنظام إدارة الشحنات (Ship ERP) توفر واجهة مستخدم حديثة وسهلة الاستخدام لتصفح وإدارة وظائف النظام المختلفة.

## الميزات الرئيسية

### 🌳 القائمة الشجرية المتقدمة
- **عرض هرمي منظم**: تنظيم الوظائف في شكل شجري سهل التصفح
- **دعم الأيقونات**: أيقونات مرئية لكل عنصر لتحسين تجربة المستخدم
- **التلميحات التفاعلية**: معلومات إضافية عند التمرير فوق العناصر

### 🔍 البحث المتقدم
- **البحث الفوري**: البحث في الوقت الفعلي أثناء الكتابة
- **البحث متعدد اللغات**: دعم البحث بالعربية والإنجليزية
- **نتائج مرتبة**: عرض النتائج مرتبة حسب الصلة

### ⭐ نظام المفضلة
- **إضافة للمفضلة**: إمكانية إضافة العناصر المستخدمة بكثرة للمفضلة
- **الوصول السريع**: عرض المفضلة في قسم منفصل للوصول السريع
- **إحصائيات الاستخدام**: تتبع عدد مرات استخدام كل عنصر

### 🔐 إدارة الصلاحيات
- **التحكم في الوصول**: إخفاء العناصر غير المسموح بالوصول إليها
- **صلاحيات متدرجة**: دعم صلاحيات على مستوى الأدوار والمستخدمين
- **تحديث ديناميكي**: تحديث الصلاحيات في الوقت الفعلي

## البنية التقنية

### الملفات الرئيسية

```
src/main/java/com/shipment/erp/
├── model/ui/
│   ├── TreeMenuItem.java          # نموذج بيانات عنصر القائمة
│   └── TreeMenuData.java          # مدير بيانات القائمة
├── service/
│   └── TreeMenuService.java       # خدمة إدارة القائمة
├── ui/
│   ├── components/
│   │   └── AdvancedTreeMenu.java  # مكون القائمة الشجرية
│   └── MainWindow.java            # النافذة الرئيسية
└── TreeMenuDemo.java              # تطبيق تجريبي
```

### المكونات الأساسية

#### 1. TreeMenuItem
```java
// خصائص العنصر
private String id;              // المعرف الفريد
private String title;           // العنوان بالعربية
private String titleEn;         // العنوان بالإنجليزية
private String description;     // الوصف
private ImageIcon icon;         // الأيقونة
private String actionType;      // نوع الإجراء (VIEW, DIALOG, REPORT)
private String targetView;      // المسار المستهدف
private boolean favorite;       // مفضل أم لا
private int accessCount;        // عدد مرات الوصول
```

#### 2. AdvancedTreeMenu
```java
// الميزات الرئيسية
- البحث الفوري في القائمة
- عرض الأيقونات والتلميحات
- قوائم السياق التفاعلية
- توسيع/طي العقد
- دعم السحب والإفلات (قيد التطوير)
```

## كيفية الاستخدام

### 1. التشغيل السريع

```bash
# تشغيل التطبيق التجريبي
./run-tree-menu.bat

# أو تشغيل النافذة الرئيسية
java -cp target/classes com.shipment.erp.ui.MainWindow
```

### 2. التفاعل مع القائمة

#### البحث
- اكتب في مربع البحث للعثور على العناصر
- النتائج تظهر فورياً أثناء الكتابة
- اضغط Enter أو انقر على النتيجة للانتقال إليها

#### التنقل
- **نقرة واحدة**: تحديد العنصر وعرض معلوماته
- **نقرة مزدوجة**: فتح العنصر وتنفيذ إجراءه
- **النقر بالزر الأيمن**: إظهار قائمة السياق

#### المفضلة
- انقر بالزر الأيمن واختر "مفضل" لإضافة/إزالة من المفضلة
- استخدم زر "المفضلة" في شريط الأدوات لعرض المفضلة فقط

### 3. التخصيص

#### إضافة عناصر جديدة
```java
TreeMenuItem newItem = new TreeMenuItem(
    "custom.item",           // المعرف
    "عنصر مخصص",            // العنوان
    "Custom Item",           // العنوان الإنجليزي
    "وصف العنصر",           // الوصف
    "/images/custom.png",    // مسار الأيقونة
    "VIEW",                  // نوع الإجراء
    "/fxml/custom.fxml"      // المسار المستهدف
);

// إضافة صلاحيات
newItem.setRequiredPermission("CUSTOM_PERMISSION");

// إضافة للقائمة
menuData.addRootItem(newItem);
```

#### تخصيص المظهر
```java
// إخفاء/إظهار الأيقونات
treeMenu.setShowIcons(false);

// إخفاء/إظهار التلميحات
treeMenu.setShowTooltips(false);

// إخفاء/إظهار البحث
treeMenu.setSearchEnabled(false);
```

## الأقسام المتاحة

### 🔧 نظام الإعدادات
- **المتغيرات العامة**: إعدادات النظام الأساسية
- **السنة المالية**: إدارة السنوات المالية
- **إدارة العملات**: العملات وأسعار الصرف
- **بيانات الشركة**: معلومات الشركة والفروع
- **إدارة المستخدمين**: المستخدمين والصلاحيات

### 📦 إدارة الأصناف
- **تصنيفات الأصناف**: تنظيم الأصناف في مجموعات
- **قائمة الأصناف**: عرض وإدارة جميع الأصناف

### 🏢 إدارة الموردين
- **قائمة الموردين**: معلومات الموردين والعلاقات التجارية

### 🚚 إدارة الشحنات
- **قائمة الشحنات**: متابعة جميع الشحنات
- **تتبع الشحنات**: تتبع حالة الشحنات في الوقت الفعلي

### 💰 إدارة المبيعات
- **إدارة العملاء**: قاعدة بيانات العملاء
- **إدارة الطلبات**: طلبات العملاء ومتابعتها
- **الفواتير**: فواتير المبيعات

### 🛒 إدارة المشتريات
- **أوامر الشراء**: إدارة أوامر الشراء من الموردين
- **إشعارات الاستلام**: تسجيل استلام البضائع

### 📊 المحاسبة
- **دليل الحسابات**: إدارة الحسابات المحاسبية
- **القيود اليومية**: تسجيل العمليات المحاسبية
- **ميزان المراجعة**: التقارير المحاسبية

### 📈 التقارير
- **التقارير المالية**: تقارير الأرباح والخسائر
- **تقارير المخزون**: حالة المخزون والحركة

## المتطلبات التقنية

### متطلبات التشغيل
- **Java**: الإصدار 8 أو أحدث
- **نظام التشغيل**: Windows, Linux, macOS
- **الذاكرة**: 512 MB RAM كحد أدنى
- **مساحة القرص**: 100 MB للتطبيق

### المكتبات المستخدمة
- **Swing**: واجهة المستخدم الرسومية
- **SLF4J**: نظام السجلات
- **Jackson**: معالجة JSON للإعدادات
- **Spring**: حقن التبعيات (اختياري)

## الاستكشاف والإصلاح

### المشاكل الشائعة

#### 1. لا تظهر الأيقونات
```java
// تأكد من وجود الأيقونات في مجلد resources
// تحقق من مسارات الأيقونات في TreeMenuItem
```

#### 2. البحث لا يعمل
```java
// تأكد من تفعيل البحث
treeMenu.setSearchEnabled(true);
```

#### 3. الصلاحيات لا تعمل
```java
// تحديث صلاحيات المستخدم
menuService.updateMenuPermissions();
```

## التطوير المستقبلي

### الميزات المخططة
- [ ] السحب والإفلات لإعادة ترتيب العناصر
- [ ] دعم السمات المتعددة (Themes)
- [ ] تصدير/استيراد تكوين القائمة
- [ ] دعم الاختصارات (Shortcuts)
- [ ] تكامل مع قاعدة البيانات
- [ ] دعم التحديث التلقائي للقائمة

### المساهمة في التطوير
نرحب بالمساهمات في تطوير القائمة الشجرية. يرجى اتباع معايير الكود المعتمدة وإضافة الاختبارات المناسبة.

---

**تم تطوير هذه القائمة الشجرية كجزء من نظام إدارة الشحنات Ship ERP**

للدعم التقني أو الاستفسارات، يرجى التواصل مع فريق التطوير.
