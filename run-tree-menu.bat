@echo off
echo ========================================
echo تشغيل تجربة القائمة الشجرية المتقدمة
echo Ship ERP - Advanced Tree Menu Demo
echo ========================================
echo.

REM التحقق من وجود Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Java 8 أو أحدث
    pause
    exit /b 1
)

echo تم العثور على Java...
echo.

REM التحقق من وجود ملفات الكلاسات
if not exist "src\main\java\TreeMenuDemo.java" (
    echo خطأ: ملف TreeMenuDemo.java غير موجود
    pause
    exit /b 1
)

echo تجميع الملفات...
echo.

REM إنشاء مجلد الكلاسات
if not exist "target\classes" mkdir "target\classes"

REM تجميع الملفات
javac -cp "target\classes" -d "target\classes" src\main\java\com\shipment\erp\model\ui\*.java
if %errorlevel% neq 0 (
    echo خطأ في تجميع ملفات النموذج
    pause
    exit /b 1
)

javac -cp "target\classes" -d "target\classes" src\main\java\com\shipment\erp\service\*.java
if %errorlevel% neq 0 (
    echo خطأ في تجميع ملفات الخدمات
    pause
    exit /b 1
)

javac -cp "target\classes" -d "target\classes" src\main\java\com\shipment\erp\ui\components\*.java
if %errorlevel% neq 0 (
    echo خطأ في تجميع ملفات المكونات
    pause
    exit /b 1
)

javac -cp "target\classes" -d "target\classes" src\main\java\TreeMenuDemo.java
if %errorlevel% neq 0 (
    echo خطأ في تجميع ملف التجربة
    pause
    exit /b 1
)

echo تم التجميع بنجاح!
echo.

echo تشغيل التطبيق...
echo.

REM تشغيل التطبيق
java -cp "target\classes" TreeMenuDemo

echo.
echo انتهى التطبيق
pause
