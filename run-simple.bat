@echo off
echo ========================================
echo Ship ERP - Tree Menu Demo
echo ========================================
echo.

REM Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java not found
    echo Please install Java 8 or newer
    pause
    exit /b 1
)

echo Java found...
echo.

REM Create directories
echo Creating directories...
if not exist "target" mkdir "target"
if not exist "target\classes" mkdir "target\classes"
if not exist "target\classes\com" mkdir "target\classes\com"
if not exist "target\classes\com\shipment" mkdir "target\classes\com\shipment"
if not exist "target\classes\com\shipment\erp" mkdir "target\classes\com\shipment\erp"
if not exist "target\classes\com\shipment\erp\model" mkdir "target\classes\com\shipment\erp\model"
if not exist "target\classes\com\shipment\erp\model\ui" mkdir "target\classes\com\shipment\erp\model\ui"
if not exist "target\classes\com\shipment\erp\service" mkdir "target\classes\com\shipment\erp\service"
if not exist "target\classes\com\shipment\erp\ui" mkdir "target\classes\com\shipment\erp\ui"
if not exist "target\classes\com\shipment\erp\ui\components" mkdir "target\classes\com\shipment\erp\ui\components"

echo.
echo Compiling...
echo.

REM Compile TreeMenuItem
echo Compiling TreeMenuItem...
javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "src\main\java\com\shipment\erp\model\ui\TreeMenuItem.java"
if %errorlevel% neq 0 (
    echo Error compiling TreeMenuItem.java
    pause
    exit /b 1
)

REM Compile TreeMenuData
echo Compiling TreeMenuData...
javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "src\main\java\com\shipment\erp\model\ui\TreeMenuData.java"
if %errorlevel% neq 0 (
    echo Error compiling TreeMenuData.java
    pause
    exit /b 1
)

REM Create simple service
echo Creating simple service...
echo package com.shipment.erp.service; > "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo import com.shipment.erp.model.ui.TreeMenuData; >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo public class TreeMenuService { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     private TreeMenuData menuData; >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     public TreeMenuService() { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo         this.menuData = new TreeMenuData(); >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     public TreeMenuData getMenuData() { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo         return menuData; >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     public void updateMenuPermissions() { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo         // Update permissions >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo. >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     public void saveMenuConfiguration() { >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo         // Save configuration >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo     } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"
echo } >> "target\classes\com\shipment\erp\service\TreeMenuService.java"

javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "target\classes\com\shipment\erp\service\TreeMenuService.java"
if %errorlevel% neq 0 (
    echo Error compiling TreeMenuService.java
    pause
    exit /b 1
)

REM Compile SimpleTreeMenuDemo
echo Compiling SimpleTreeMenuDemo...
javac -encoding UTF-8 -cp "target\classes" -d "target\classes" "src\main\java\SimpleTreeMenuDemo.java"
if %errorlevel% neq 0 (
    echo Error compiling SimpleTreeMenuDemo.java
    pause
    exit /b 1
)

echo.
echo Compilation successful!
echo.

echo Running application...
echo.

REM Run application
java -Dfile.encoding=UTF-8 -cp "target\classes" SimpleTreeMenuDemo

echo.
echo Application finished
echo.
pause
