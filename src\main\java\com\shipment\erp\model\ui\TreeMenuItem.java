package com.shipment.erp.model.ui;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * نموذج بيانات عنصر القائمة الشجرية
 * يمثل عنصر واحد في القائمة الشجرية مع دعم الأيقونات والصلاحيات
 */
public class TreeMenuItem {
    
    // الخصائص الأساسية
    private String id;
    private String title;
    private String titleEn;
    private String description;
    private String iconPath;
    private ImageIcon icon;
    
    // خصائص الحالة
    private boolean enabled = true;
    private boolean visible = true;
    private boolean expanded = false;
    private boolean leaf = true;
    
    // خصائص الصلاحيات
    private String requiredPermission;
    private String requiredRole;
    private boolean hasPermission = true;
    
    // خصائص التصنيف والترتيب
    private int sortOrder = 0;
    private String category;
    private String group;
    
    // خصائص الإجراءات
    private String actionType;
    private String targetView;
    private String targetController;
    private Runnable action;
    
    // العناصر الفرعية
    private final List<TreeMenuItem> children = new ArrayList<>();
    private TreeMenuItem parent;
    
    // خصائص إضافية
    private String tooltip;
    private String shortcut;
    private boolean favorite = false;
    private int accessCount = 0;

    /**
     * منشئ افتراضي
     */
    public TreeMenuItem() {
        updateLeafStatus();
    }
    
    /**
     * منشئ مع المعاملات الأساسية
     */
    public TreeMenuItem(String id, String title, String iconPath) {
        this();
        this.id = id;
        this.title = title;
        setIconPath(iconPath);
    }
    
    /**
     * منشئ مع المعاملات الكاملة
     */
    public TreeMenuItem(String id, String title, String titleEn, String description, 
                       String iconPath, String actionType, String targetView) {
        this(id, title, iconPath);
        this.titleEn = titleEn;
        this.description = description;
        this.actionType = actionType;
        this.targetView = targetView;
    }

    // Getters and Setters للخصائص الأساسية
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getTitleEn() { return titleEn; }
    public void setTitleEn(String titleEn) { this.titleEn = titleEn; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getIconPath() { return iconPath; }
    public void setIconPath(String iconPath) { 
        this.iconPath = iconPath;
        // تحميل الأيقونة تلقائياً
        if (iconPath != null && !iconPath.isEmpty()) {
            try {
                java.net.URL iconUrl = getClass().getResource(iconPath);
                if (iconUrl != null) {
                    this.icon = new ImageIcon(iconUrl);
                } else {
                    this.icon = null;
                }
            } catch (Exception e) {
                // في حالة فشل تحميل الأيقونة، استخدم أيقونة افتراضية
                this.icon = null;
            }
        }
    }
    
    public ImageIcon getIcon() { return icon; }
    public void setIcon(ImageIcon icon) { this.icon = icon; }

    // Getters and Setters لخصائص الحالة
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public boolean isVisible() { return visible; }
    public void setVisible(boolean visible) { this.visible = visible; }
    
    public boolean isExpanded() { return expanded; }
    public void setExpanded(boolean expanded) { this.expanded = expanded; }
    
    public boolean isLeaf() { return leaf; }
    
    // Getters and Setters للصلاحيات
    public String getRequiredPermission() { return requiredPermission; }
    public void setRequiredPermission(String requiredPermission) { this.requiredPermission = requiredPermission; }
    
    public String getRequiredRole() { return requiredRole; }
    public void setRequiredRole(String requiredRole) { this.requiredRole = requiredRole; }
    
    public boolean hasPermission() { return hasPermission; }
    public void setHasPermission(boolean hasPermission) { this.hasPermission = hasPermission; }
    
    // Getters and Setters للتصنيف والترتيب
    public int getSortOrder() { return sortOrder; }
    public void setSortOrder(int sortOrder) { this.sortOrder = sortOrder; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public String getGroup() { return group; }
    public void setGroup(String group) { this.group = group; }
    
    // Getters and Setters للإجراءات
    public String getActionType() { return actionType; }
    public void setActionType(String actionType) { this.actionType = actionType; }
    
    public String getTargetView() { return targetView; }
    public void setTargetView(String targetView) { this.targetView = targetView; }
    
    public String getTargetController() { return targetController; }
    public void setTargetController(String targetController) { this.targetController = targetController; }
    
    public Runnable getAction() { return action; }
    public void setAction(Runnable action) { this.action = action; }
    
    // إدارة العناصر الفرعية
    public List<TreeMenuItem> getChildren() { return children; }
    
    public TreeMenuItem getParent() { return parent; }
    public void setParent(TreeMenuItem parent) { this.parent = parent; }
    
    /**
     * إضافة عنصر فرعي
     */
    public void addChild(TreeMenuItem child) {
        child.setParent(this);
        children.add(child);
        updateLeafStatus();
    }
    
    /**
     * إزالة عنصر فرعي
     */
    public void removeChild(TreeMenuItem child) {
        child.setParent(null);
        children.remove(child);
        updateLeafStatus();
    }
    
    /**
     * تحديث حالة الورقة
     */
    private void updateLeafStatus() {
        this.leaf = children.isEmpty();
    }
    
    /**
     * البحث عن عنصر فرعي بالمعرف
     */
    public TreeMenuItem findChildById(String id) {
        for (TreeMenuItem child : children) {
            if (Objects.equals(id, child.getId())) {
                return child;
            }
            TreeMenuItem found = child.findChildById(id);
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    // خصائص إضافية
    public String getTooltip() { return tooltip; }
    public void setTooltip(String tooltip) { this.tooltip = tooltip; }
    
    public String getShortcut() { return shortcut; }
    public void setShortcut(String shortcut) { this.shortcut = shortcut; }
    
    public boolean isFavorite() { return favorite; }
    public void setFavorite(boolean favorite) { this.favorite = favorite; }
    
    public int getAccessCount() { return accessCount; }
    public void setAccessCount(int accessCount) { this.accessCount = accessCount; }
    
    /**
     * زيادة عداد الوصول
     */
    public void incrementAccessCount() {
        setAccessCount(getAccessCount() + 1);
    }
    
    /**
     * تنفيذ الإجراء المرتبط بالعنصر
     */
    public void executeAction() {
        if (action != null) {
            action.run();
        }
        incrementAccessCount();
    }
    
    /**
     * التحقق من وجود صلاحية للوصول
     */
    public boolean canAccess() {
        return isEnabled() && isVisible() && hasPermission();
    }
    
    /**
     * الحصول على المسار الكامل للعنصر
     */
    public String getFullPath() {
        if (parent == null) {
            return getTitle();
        }
        return parent.getFullPath() + " > " + getTitle();
    }
    
    /**
     * الحصول على مستوى العنصر في الشجرة
     */
    public int getLevel() {
        if (parent == null) {
            return 0;
        }
        return parent.getLevel() + 1;
    }
    
    @Override
    public String toString() {
        return getTitle();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TreeMenuItem that = (TreeMenuItem) obj;
        return Objects.equals(getId(), that.getId());
    }
    
    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
