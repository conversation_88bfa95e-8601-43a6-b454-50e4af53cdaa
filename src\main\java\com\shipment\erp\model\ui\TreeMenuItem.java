package com.shipment.erp.model.ui;

import javafx.beans.property.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.image.Image;

import java.util.ArrayList;
import java.util.List;

/**
 * نموذج بيانات عنصر القائمة الشجرية
 * يمثل عنصر واحد في القائمة الشجرية مع دعم الأيقونات والصلاحيات
 */
public class TreeMenuItem {
    
    // الخصائص الأساسية
    private final StringProperty id = new SimpleStringProperty();
    private final StringProperty title = new SimpleStringProperty();
    private final StringProperty titleEn = new SimpleStringProperty();
    private final StringProperty description = new SimpleStringProperty();
    private final StringProperty iconPath = new SimpleStringProperty();
    private final ObjectProperty<Image> icon = new SimpleObjectProperty<>();
    
    // خصائص الحالة
    private final BooleanProperty enabled = new SimpleBooleanProperty(true);
    private final BooleanProperty visible = new SimpleBooleanProperty(true);
    private final BooleanProperty expanded = new SimpleBooleanProperty(false);
    private final BooleanProperty leaf = new SimpleBooleanProperty(true);
    
    // خصائص الصلاحيات
    private final StringProperty requiredPermission = new SimpleStringProperty();
    private final StringProperty requiredRole = new SimpleStringProperty();
    private final BooleanProperty hasPermission = new SimpleBooleanProperty(true);
    
    // خصائص التصنيف والترتيب
    private final IntegerProperty sortOrder = new SimpleIntegerProperty(0);
    private final StringProperty category = new SimpleStringProperty();
    private final StringProperty group = new SimpleStringProperty();
    
    // خصائص الإجراءات
    private final StringProperty actionType = new SimpleStringProperty();
    private final StringProperty targetView = new SimpleStringProperty();
    private final StringProperty targetController = new SimpleStringProperty();
    private final ObjectProperty<Runnable> action = new SimpleObjectProperty<>();
    
    // العناصر الفرعية
    private final ObservableList<TreeMenuItem> children = FXCollections.observableArrayList();
    private TreeMenuItem parent;
    
    // خصائص إضافية
    private final StringProperty tooltip = new SimpleStringProperty();
    private final StringProperty shortcut = new SimpleStringProperty();
    private final BooleanProperty favorite = new SimpleBooleanProperty(false);
    private final IntegerProperty accessCount = new SimpleIntegerProperty(0);
    
    /**
     * منشئ افتراضي
     */
    public TreeMenuItem() {
        // ربط خاصية leaf بوجود عناصر فرعية
        leaf.bind(children.emptyProperty());
    }
    
    /**
     * منشئ مع المعاملات الأساسية
     */
    public TreeMenuItem(String id, String title, String iconPath) {
        this();
        setId(id);
        setTitle(title);
        setIconPath(iconPath);
    }
    
    /**
     * منشئ مع المعاملات الكاملة
     */
    public TreeMenuItem(String id, String title, String titleEn, String description, 
                       String iconPath, String actionType, String targetView) {
        this(id, title, iconPath);
        setTitleEn(titleEn);
        setDescription(description);
        setActionType(actionType);
        setTargetView(targetView);
    }
    
    // Getters and Setters للخصائص الأساسية
    public String getId() { return id.get(); }
    public void setId(String id) { this.id.set(id); }
    public StringProperty idProperty() { return id; }
    
    public String getTitle() { return title.get(); }
    public void setTitle(String title) { this.title.set(title); }
    public StringProperty titleProperty() { return title; }
    
    public String getTitleEn() { return titleEn.get(); }
    public void setTitleEn(String titleEn) { this.titleEn.set(titleEn); }
    public StringProperty titleEnProperty() { return titleEn; }
    
    public String getDescription() { return description.get(); }
    public void setDescription(String description) { this.description.set(description); }
    public StringProperty descriptionProperty() { return description; }
    
    public String getIconPath() { return iconPath.get(); }
    public void setIconPath(String iconPath) { 
        this.iconPath.set(iconPath);
        // تحميل الأيقونة تلقائياً
        if (iconPath != null && !iconPath.isEmpty()) {
            try {
                Image image = new Image(getClass().getResourceAsStream(iconPath));
                setIcon(image);
            } catch (Exception e) {
                // في حالة فشل تحميل الأيقونة، استخدم أيقونة افتراضية
                setIcon(null);
            }
        }
    }
    public StringProperty iconPathProperty() { return iconPath; }
    
    public Image getIcon() { return icon.get(); }
    public void setIcon(Image icon) { this.icon.set(icon); }
    public ObjectProperty<Image> iconProperty() { return icon; }
    
    // Getters and Setters لخصائص الحالة
    public boolean isEnabled() { return enabled.get(); }
    public void setEnabled(boolean enabled) { this.enabled.set(enabled); }
    public BooleanProperty enabledProperty() { return enabled; }
    
    public boolean isVisible() { return visible.get(); }
    public void setVisible(boolean visible) { this.visible.set(visible); }
    public BooleanProperty visibleProperty() { return visible; }
    
    public boolean isExpanded() { return expanded.get(); }
    public void setExpanded(boolean expanded) { this.expanded.set(expanded); }
    public BooleanProperty expandedProperty() { return expanded; }
    
    public boolean isLeaf() { return leaf.get(); }
    public BooleanProperty leafProperty() { return leaf; }
    
    // Getters and Setters للصلاحيات
    public String getRequiredPermission() { return requiredPermission.get(); }
    public void setRequiredPermission(String requiredPermission) { this.requiredPermission.set(requiredPermission); }
    public StringProperty requiredPermissionProperty() { return requiredPermission; }
    
    public String getRequiredRole() { return requiredRole.get(); }
    public void setRequiredRole(String requiredRole) { this.requiredRole.set(requiredRole); }
    public StringProperty requiredRoleProperty() { return requiredRole; }
    
    public boolean hasPermission() { return hasPermission.get(); }
    public void setHasPermission(boolean hasPermission) { this.hasPermission.set(hasPermission); }
    public BooleanProperty hasPermissionProperty() { return hasPermission; }
    
    // Getters and Setters للتصنيف والترتيب
    public int getSortOrder() { return sortOrder.get(); }
    public void setSortOrder(int sortOrder) { this.sortOrder.set(sortOrder); }
    public IntegerProperty sortOrderProperty() { return sortOrder; }
    
    public String getCategory() { return category.get(); }
    public void setCategory(String category) { this.category.set(category); }
    public StringProperty categoryProperty() { return category; }
    
    public String getGroup() { return group.get(); }
    public void setGroup(String group) { this.group.set(group); }
    public StringProperty groupProperty() { return group; }
    
    // Getters and Setters للإجراءات
    public String getActionType() { return actionType.get(); }
    public void setActionType(String actionType) { this.actionType.set(actionType); }
    public StringProperty actionTypeProperty() { return actionType; }
    
    public String getTargetView() { return targetView.get(); }
    public void setTargetView(String targetView) { this.targetView.set(targetView); }
    public StringProperty targetViewProperty() { return targetView; }
    
    public String getTargetController() { return targetController.get(); }
    public void setTargetController(String targetController) { this.targetController.set(targetController); }
    public StringProperty targetControllerProperty() { return targetController; }
    
    public Runnable getAction() { return action.get(); }
    public void setAction(Runnable action) { this.action.set(action); }
    public ObjectProperty<Runnable> actionProperty() { return action; }
    
    // إدارة العناصر الفرعية
    public ObservableList<TreeMenuItem> getChildren() { return children; }
    
    public TreeMenuItem getParent() { return parent; }
    public void setParent(TreeMenuItem parent) { this.parent = parent; }
    
    /**
     * إضافة عنصر فرعي
     */
    public void addChild(TreeMenuItem child) {
        child.setParent(this);
        children.add(child);
    }
    
    /**
     * إزالة عنصر فرعي
     */
    public void removeChild(TreeMenuItem child) {
        child.setParent(null);
        children.remove(child);
    }
    
    /**
     * البحث عن عنصر فرعي بالمعرف
     */
    public TreeMenuItem findChildById(String id) {
        for (TreeMenuItem child : children) {
            if (id.equals(child.getId())) {
                return child;
            }
            TreeMenuItem found = child.findChildById(id);
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    // خصائص إضافية
    public String getTooltip() { return tooltip.get(); }
    public void setTooltip(String tooltip) { this.tooltip.set(tooltip); }
    public StringProperty tooltipProperty() { return tooltip; }
    
    public String getShortcut() { return shortcut.get(); }
    public void setShortcut(String shortcut) { this.shortcut.set(shortcut); }
    public StringProperty shortcutProperty() { return shortcut; }
    
    public boolean isFavorite() { return favorite.get(); }
    public void setFavorite(boolean favorite) { this.favorite.set(favorite); }
    public BooleanProperty favoriteProperty() { return favorite; }
    
    public int getAccessCount() { return accessCount.get(); }
    public void setAccessCount(int accessCount) { this.accessCount.set(accessCount); }
    public IntegerProperty accessCountProperty() { return accessCount; }
    
    /**
     * زيادة عداد الوصول
     */
    public void incrementAccessCount() {
        setAccessCount(getAccessCount() + 1);
    }
    
    /**
     * تنفيذ الإجراء المرتبط بالعنصر
     */
    public void executeAction() {
        if (action.get() != null) {
            action.get().run();
        }
        incrementAccessCount();
    }
    
    /**
     * التحقق من وجود صلاحية للوصول
     */
    public boolean canAccess() {
        return isEnabled() && isVisible() && hasPermission();
    }
    
    /**
     * الحصول على المسار الكامل للعنصر
     */
    public String getFullPath() {
        if (parent == null) {
            return getTitle();
        }
        return parent.getFullPath() + " > " + getTitle();
    }
    
    /**
     * الحصول على مستوى العنصر في الشجرة
     */
    public int getLevel() {
        if (parent == null) {
            return 0;
        }
        return parent.getLevel() + 1;
    }
    
    @Override
    public String toString() {
        return getTitle();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TreeMenuItem that = (TreeMenuItem) obj;
        return getId() != null ? getId().equals(that.getId()) : that.getId() == null;
    }
    
    @Override
    public int hashCode() {
        return getId() != null ? getId().hashCode() : 0;
    }
}
