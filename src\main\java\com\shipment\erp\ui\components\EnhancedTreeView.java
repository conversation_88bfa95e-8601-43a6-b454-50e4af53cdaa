package com.shipment.erp.ui.components;

import com.shipment.erp.model.ui.TreeMenuItem;
import com.shipment.erp.model.ui.TreeMenuData;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.ImageView;
import javafx.scene.input.KeyCode;
import javafx.scene.input.MouseButton;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.util.Callback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.function.Consumer;

/**
 * قائمة شجرية محسنة مع ميزات متقدمة
 * تدعم البحث والفلترة والسحب والإفلات
 */
public class EnhancedTreeView extends VBox {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedTreeView.class);
    
    // المكونات الرئيسية
    private final TextField searchField;
    private final TreeView<TreeMenuItem> treeView;
    private final TreeMenuData menuData;
    
    // خصائص التحكم
    private final BooleanProperty searchEnabled = new SimpleBooleanProperty(true);
    private final StringProperty searchPrompt = new SimpleStringProperty("البحث في القائمة...");
    private final BooleanProperty showIcons = new SimpleBooleanProperty(true);
    private final BooleanProperty showTooltips = new SimpleBooleanProperty(true);
    
    // معالجات الأحداث
    private Consumer<TreeMenuItem> onItemSelected;
    private Consumer<TreeMenuItem> onItemDoubleClicked;
    private Consumer<TreeMenuItem> onItemRightClicked;
    
    // بيانات البحث والفلترة
    private final ObservableList<TreeMenuItem> filteredItems = FXCollections.observableArrayList();
    private String currentSearchText = "";
    
    /**
     * منشئ افتراضي
     */
    public EnhancedTreeView() {
        this(new TreeMenuData());
    }
    
    /**
     * منشئ مع بيانات القائمة
     */
    public EnhancedTreeView(TreeMenuData menuData) {
        this.menuData = menuData;
        
        // إنشاء حقل البحث
        this.searchField = createSearchField();
        
        // إنشاء القائمة الشجرية
        this.treeView = createTreeView();
        
        // تهيئة الواجهة
        initializeUI();
        
        // تحميل البيانات
        loadTreeData();
        
        logger.info("تم إنشاء القائمة الشجرية المحسنة");
    }
    
    /**
     * إنشاء حقل البحث
     */
    private TextField createSearchField() {
        TextField field = new TextField();
        field.setPromptText(searchPrompt.get());
        field.getStyleClass().add("search-field");
        
        // ربط النص التوضيحي
        field.promptTextProperty().bind(searchPrompt);
        
        // معالج تغيير النص
        field.textProperty().addListener((obs, oldText, newText) -> {
            currentSearchText = newText;
            Platform.runLater(this::filterTree);
        });
        
        // معالج الضغط على Enter
        field.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                performSearch();
            } else if (event.getCode() == KeyCode.ESCAPE) {
                field.clear();
            }
        });
        
        return field;
    }
    
    /**
     * إنشاء القائمة الشجرية
     */
    private TreeView<TreeMenuItem> createTreeView() {
        TreeView<TreeMenuItem> tree = new TreeView<>();
        tree.setShowRoot(false);
        tree.getStyleClass().add("enhanced-tree-view");
        
        // إعداد مصنع الخلايا
        tree.setCellFactory(createCellFactory());
        
        // معالجات الأحداث
        tree.setOnMouseClicked(event -> {
            TreeItem<TreeMenuItem> selectedItem = tree.getSelectionModel().getSelectedItem();
            if (selectedItem != null && selectedItem.getValue() != null) {
                TreeMenuItem menuItem = selectedItem.getValue();
                
                if (event.getButton() == MouseButton.PRIMARY) {
                    if (event.getClickCount() == 1) {
                        handleItemSelected(menuItem);
                    } else if (event.getClickCount() == 2) {
                        handleItemDoubleClicked(menuItem);
                    }
                } else if (event.getButton() == MouseButton.SECONDARY) {
                    handleItemRightClicked(menuItem);
                }
            }
        });
        
        // معالج لوحة المفاتيح
        tree.setOnKeyPressed(event -> {
            TreeItem<TreeMenuItem> selectedItem = tree.getSelectionModel().getSelectedItem();
            if (selectedItem != null && selectedItem.getValue() != null) {
                TreeMenuItem menuItem = selectedItem.getValue();
                
                if (event.getCode() == KeyCode.ENTER) {
                    handleItemDoubleClicked(menuItem);
                } else if (event.getCode() == KeyCode.SPACE) {
                    selectedItem.setExpanded(!selectedItem.isExpanded());
                }
            }
        });
        
        return tree;
    }
    
    /**
     * إنشاء مصنع الخلايا المخصص
     */
    private Callback<TreeView<TreeMenuItem>, TreeCell<TreeMenuItem>> createCellFactory() {
        return treeView -> new TreeCell<TreeMenuItem>() {
            private final HBox container = new HBox(5);
            private final ImageView iconView = new ImageView();
            private final Label textLabel = new Label();
            private final Label shortcutLabel = new Label();
            
            {
                container.setAlignment(Pos.CENTER_LEFT);
                iconView.setFitWidth(16);
                iconView.setFitHeight(16);
                iconView.setPreserveRatio(true);
                
                textLabel.getStyleClass().add("tree-item-text");
                shortcutLabel.getStyleClass().add("tree-item-shortcut");
                
                HBox.setHgrow(textLabel, Priority.ALWAYS);
                container.getChildren().addAll(iconView, textLabel, shortcutLabel);
            }
            
            @Override
            protected void updateItem(TreeMenuItem item, boolean empty) {
                super.updateItem(item, empty);
                
                if (empty || item == null) {
                    setGraphic(null);
                    setText(null);
                    setTooltip(null);
                } else {
                    // تعيين النص
                    textLabel.setText(item.getTitle());
                    
                    // تعيين الأيقونة
                    if (showIcons.get() && item.getIcon() != null) {
                        iconView.setImage(item.getIcon());
                        iconView.setVisible(true);
                    } else {
                        iconView.setVisible(false);
                    }
                    
                    // تعيين الاختصار
                    if (item.getShortcut() != null && !item.getShortcut().isEmpty()) {
                        shortcutLabel.setText(item.getShortcut());
                        shortcutLabel.setVisible(true);
                    } else {
                        shortcutLabel.setVisible(false);
                    }
                    
                    // تعيين التلميح
                    if (showTooltips.get() && item.getTooltip() != null && !item.getTooltip().isEmpty()) {
                        setTooltip(new Tooltip(item.getTooltip()));
                    }
                    
                    // تطبيق الأنماط حسب الحالة
                    getStyleClass().removeAll("disabled-item", "favorite-item", "recent-item");
                    
                    if (!item.canAccess()) {
                        getStyleClass().add("disabled-item");
                        setDisable(true);
                    } else {
                        setDisable(false);
                        
                        if (item.isFavorite()) {
                            getStyleClass().add("favorite-item");
                        }
                        
                        if (item.getAccessCount() > 0) {
                            getStyleClass().add("recent-item");
                        }
                    }
                    
                    setGraphic(container);
                }
            }
        };
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeUI() {
        setSpacing(5);
        setPadding(new Insets(5));
        getStyleClass().add("enhanced-tree-container");
        
        // إضافة المكونات
        getChildren().addAll(searchField, treeView);
        
        // تعيين نمو المكونات
        VBox.setVgrow(treeView, Priority.ALWAYS);
        
        // ربط خاصية إظهار البحث
        searchField.visibleProperty().bind(searchEnabled);
        searchField.managedProperty().bind(searchEnabled);
    }
    
    /**
     * تحميل بيانات الشجرة
     */
    private void loadTreeData() {
        TreeItem<TreeMenuItem> root = new TreeItem<>();
        
        for (TreeMenuItem rootItem : menuData.getRootItems()) {
            if (rootItem.canAccess()) {
                TreeItem<TreeMenuItem> treeItem = createTreeItem(rootItem);
                root.getChildren().add(treeItem);
            }
        }
        
        treeView.setRoot(root);
        
        // توسيع العقد الرئيسية
        expandMainNodes();
    }
    
    /**
     * إنشاء عنصر شجري من عنصر القائمة
     */
    private TreeItem<TreeMenuItem> createTreeItem(TreeMenuItem menuItem) {
        TreeItem<TreeMenuItem> treeItem = new TreeItem<>(menuItem);
        
        // إضافة العناصر الفرعية
        for (TreeMenuItem child : menuItem.getChildren()) {
            if (child.canAccess()) {
                TreeItem<TreeMenuItem> childItem = createTreeItem(child);
                treeItem.getChildren().add(childItem);
            }
        }
        
        // ربط حالة التوسيع
        treeItem.expandedProperty().bindBidirectional(menuItem.expandedProperty());
        
        return treeItem;
    }
    
    /**
     * توسيع العقد الرئيسية
     */
    private void expandMainNodes() {
        if (treeView.getRoot() != null) {
            for (TreeItem<TreeMenuItem> item : treeView.getRoot().getChildren()) {
                item.setExpanded(true);
            }
        }
    }
    
    /**
     * تنفيذ البحث
     */
    private void performSearch() {
        if (currentSearchText.trim().isEmpty()) {
            loadTreeData();
            return;
        }
        
        List<TreeMenuItem> searchResults = menuData.searchItems(currentSearchText);
        filteredItems.setAll(searchResults);
        
        // إنشاء شجرة البحث
        TreeItem<TreeMenuItem> searchRoot = new TreeItem<>();
        
        for (TreeMenuItem item : searchResults) {
            if (item.canAccess()) {
                TreeItem<TreeMenuItem> resultItem = new TreeItem<>(item);
                searchRoot.getChildren().add(resultItem);
            }
        }
        
        treeView.setRoot(searchRoot);
        
        // توسيع جميع النتائج
        for (TreeItem<TreeMenuItem> item : searchRoot.getChildren()) {
            item.setExpanded(true);
        }
        
        logger.debug("تم العثور على {} نتيجة للبحث: {}", searchResults.size(), currentSearchText);
    }
    
    /**
     * فلترة الشجرة
     */
    private void filterTree() {
        if (currentSearchText.trim().isEmpty()) {
            loadTreeData();
        } else {
            performSearch();
        }
    }
    
    /**
     * معالج تحديد العنصر
     */
    private void handleItemSelected(TreeMenuItem item) {
        if (onItemSelected != null) {
            onItemSelected.accept(item);
        }
        logger.debug("تم تحديد العنصر: {}", item.getTitle());
    }
    
    /**
     * معالج النقر المزدوج
     */
    private void handleItemDoubleClicked(TreeMenuItem item) {
        if (item.isLeaf()) {
            item.executeAction();
            if (onItemDoubleClicked != null) {
                onItemDoubleClicked.accept(item);
            }
            logger.info("تم تنفيذ العنصر: {}", item.getTitle());
        }
    }
    
    /**
     * معالج النقر بالزر الأيمن
     */
    private void handleItemRightClicked(TreeMenuItem item) {
        if (onItemRightClicked != null) {
            onItemRightClicked.accept(item);
        }
        showContextMenu(item);
    }
    
    /**
     * عرض القائمة السياقية
     */
    private void showContextMenu(TreeMenuItem item) {
        ContextMenu contextMenu = new ContextMenu();
        
        // إضافة/إزالة من المفضلة
        MenuItem favoriteItem = new MenuItem(
            item.isFavorite() ? "إزالة من المفضلة" : "إضافة للمفضلة"
        );
        favoriteItem.setOnAction(e -> {
            item.setFavorite(!item.isFavorite());
            treeView.refresh();
        });
        
        // نسخ المسار
        MenuItem copyPathItem = new MenuItem("نسخ المسار");
        copyPathItem.setOnAction(e -> {
            // تنفيذ نسخ المسار
            logger.info("تم نسخ المسار: {}", item.getFullPath());
        });
        
        contextMenu.getItems().addAll(favoriteItem, copyPathItem);
        
        // عرض القائمة
        contextMenu.show(treeView, 0, 0);
    }
    
    // Getters and Setters
    public TreeMenuData getMenuData() { return menuData; }
    
    public TreeView<TreeMenuItem> getTreeView() { return treeView; }
    
    public TextField getSearchField() { return searchField; }
    
    public boolean isSearchEnabled() { return searchEnabled.get(); }
    public void setSearchEnabled(boolean searchEnabled) { this.searchEnabled.set(searchEnabled); }
    public BooleanProperty searchEnabledProperty() { return searchEnabled; }
    
    public String getSearchPrompt() { return searchPrompt.get(); }
    public void setSearchPrompt(String searchPrompt) { this.searchPrompt.set(searchPrompt); }
    public StringProperty searchPromptProperty() { return searchPrompt; }
    
    public boolean isShowIcons() { return showIcons.get(); }
    public void setShowIcons(boolean showIcons) { this.showIcons.set(showIcons); }
    public BooleanProperty showIconsProperty() { return showIcons; }
    
    public boolean isShowTooltips() { return showTooltips.get(); }
    public void setShowTooltips(boolean showTooltips) { this.showTooltips.set(showTooltips); }
    public BooleanProperty showTooltipsProperty() { return showTooltips; }
    
    // معالجات الأحداث
    public void setOnItemSelected(Consumer<TreeMenuItem> onItemSelected) {
        this.onItemSelected = onItemSelected;
    }
    
    public void setOnItemDoubleClicked(Consumer<TreeMenuItem> onItemDoubleClicked) {
        this.onItemDoubleClicked = onItemDoubleClicked;
    }
    
    public void setOnItemRightClicked(Consumer<TreeMenuItem> onItemRightClicked) {
        this.onItemRightClicked = onItemRightClicked;
    }
    
    /**
     * تحديث البيانات
     */
    public void refresh() {
        loadTreeData();
    }
    
    /**
     * مسح البحث
     */
    public void clearSearch() {
        searchField.clear();
    }
    
    /**
     * تحديد عنصر بالمعرف
     */
    public void selectItemById(String id) {
        TreeMenuItem item = menuData.findItemById(id);
        if (item != null) {
            selectItem(item);
        }
    }
    
    /**
     * تحديد عنصر
     */
    public void selectItem(TreeMenuItem item) {
        // البحث عن العنصر في الشجرة وتحديده
        TreeItem<TreeMenuItem> treeItem = findTreeItem(treeView.getRoot(), item);
        if (treeItem != null) {
            treeView.getSelectionModel().select(treeItem);
            treeView.scrollTo(treeView.getRow(treeItem));
        }
    }
    
    /**
     * البحث عن عنصر شجري
     */
    private TreeItem<TreeMenuItem> findTreeItem(TreeItem<TreeMenuItem> root, TreeMenuItem target) {
        if (root == null) return null;
        
        if (root.getValue() != null && root.getValue().equals(target)) {
            return root;
        }
        
        for (TreeItem<TreeMenuItem> child : root.getChildren()) {
            TreeItem<TreeMenuItem> found = findTreeItem(child, target);
            if (found != null) {
                return found;
            }
        }
        
        return null;
    }
}
