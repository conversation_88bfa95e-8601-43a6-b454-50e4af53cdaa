package com.shipment.erp.ui.components;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Point;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.Enumeration;
import java.util.List;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.SwingUtilities;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreeNode;
import javax.swing.tree.TreePath;

import com.shipment.erp.model.ui.TreeMenuData;
import com.shipment.erp.model.ui.TreeMenuItem;

/**
 * مكون القائمة الشجرية المتقدم
 * يوفر قائمة شجرية متقدمة مع دعم البحث والأيقونات والسحب والإفلات
 */
public class AdvancedTreeMenu extends JPanel {
    
    private final TreeMenuData menuData;
    private final JTree tree;
    private final DefaultTreeModel treeModel;
    private final DefaultMutableTreeNode rootNode;
    private final JTextField searchField;
    private final JPanel toolbarPanel;
    private final JScrollPane scrollPane;
    
    // خصائص التخصيص
    private boolean showIcons = true;
    private boolean showTooltips = true;
    private boolean searchEnabled = true;
    private boolean dragDropEnabled = true;
    
    // مستمعي الأحداث
    private TreeMenuActionListener actionListener;
    
    /**
     * واجهة مستمع أحداث القائمة
     */
    public interface TreeMenuActionListener {
        void onItemSelected(TreeMenuItem item);
        void onItemDoubleClicked(TreeMenuItem item);
        void onItemRightClicked(TreeMenuItem item, Point location);
    }
    
    /**
     * منشئ القائمة الشجرية
     */
    public AdvancedTreeMenu(TreeMenuData menuData) {
        this.menuData = menuData;
        
        // إنشاء العقدة الجذرية
        this.rootNode = new DefaultMutableTreeNode("القائمة الرئيسية");
        this.treeModel = new DefaultTreeModel(rootNode);
        this.tree = new JTree(treeModel);
        
        // إنشاء شريط البحث
        this.searchField = new JTextField();
        this.toolbarPanel = new JPanel(new BorderLayout());
        
        // إنشاء لوحة التمرير
        this.scrollPane = new JScrollPane(tree);
        
        initializeComponents();
        setupEventHandlers();
        loadMenuData();
        
        setLayout(new BorderLayout());
        add(toolbarPanel, BorderLayout.NORTH);
        add(scrollPane, BorderLayout.CENTER);
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // إعداد الشجرة
        tree.setRootVisible(false);
        tree.setShowsRootHandles(true);
        tree.setCellRenderer(new TreeMenuCellRenderer());
        tree.setRowHeight(24);
        tree.putClientProperty("JTree.lineStyle", "Angled");
        
        // إعداد شريط البحث
        searchField.setPreferredSize(new Dimension(200, 25));
        searchField.setToolTipText("البحث في القائمة...");
        
        // إعداد شريط الأدوات
        toolbarPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(searchLabel.getFont().deriveFont(Font.BOLD));
        
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        
        toolbarPanel.add(searchPanel, BorderLayout.EAST);
        
        // إعداد لوحة التمرير
        scrollPane.setPreferredSize(new Dimension(300, 400));
        scrollPane.setBorder(BorderFactory.createLoweredBevelBorder());
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    private void setupEventHandlers() {
        // مستمع النقر على الشجرة
        tree.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                handleTreeClick(e);
            }
        });
        
        // مستمع تحديد العقد
        tree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
            if (selectedNode != null && selectedNode.getUserObject() instanceof TreeMenuItem) {
                TreeMenuItem item = (TreeMenuItem) selectedNode.getUserObject();
                if (actionListener != null) {
                    actionListener.onItemSelected(item);
                }
            }
        });
        
        // مستمع البحث
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                performSearch();
            }
        });
        
        // مستمع Enter في البحث
        searchField.addActionListener(e -> performSearch());
    }
    
    /**
     * معالجة النقر على الشجرة
     */
    private void handleTreeClick(MouseEvent e) {
        TreePath path = tree.getPathForLocation(e.getX(), e.getY());
        if (path == null) return;
        
        DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
        if (!(node.getUserObject() instanceof TreeMenuItem)) return;
        
        TreeMenuItem item = (TreeMenuItem) node.getUserObject();
        
        if (SwingUtilities.isRightMouseButton(e)) {
            // النقر بالزر الأيمن
            if (actionListener != null) {
                actionListener.onItemRightClicked(item, e.getPoint());
            }
        } else if (e.getClickCount() == 2) {
            // النقر المزدوج
            if (actionListener != null) {
                actionListener.onItemDoubleClicked(item);
            }
            item.executeAction();
        }
    }
    
    /**
     * تحميل بيانات القائمة
     */
    private void loadMenuData() {
        rootNode.removeAllChildren();
        
        for (TreeMenuItem rootItem : menuData.getRootItems()) {
            if (rootItem.canAccess()) {
                DefaultMutableTreeNode node = createTreeNode(rootItem);
                rootNode.add(node);
            }
        }
        
        treeModel.reload();
        expandDefaultNodes();
    }
    
    /**
     * إنشاء عقدة شجرية من عنصر القائمة
     */
    private DefaultMutableTreeNode createTreeNode(TreeMenuItem item) {
        DefaultMutableTreeNode node = new DefaultMutableTreeNode(item);
        
        // إضافة العناصر الفرعية
        for (TreeMenuItem child : item.getChildren()) {
            if (child.canAccess()) {
                DefaultMutableTreeNode childNode = createTreeNode(child);
                node.add(childNode);
            }
        }
        
        return node;
    }
    
    /**
     * توسيع العقد الافتراضية
     */
    private void expandDefaultNodes() {
        // توسيع العقد المحددة كموسعة
        expandNodesRecursively(rootNode);
    }
    
    /**
     * توسيع العقد بشكل تكراري
     */
    private void expandNodesRecursively(DefaultMutableTreeNode node) {
        if (node.getUserObject() instanceof TreeMenuItem) {
            TreeMenuItem item = (TreeMenuItem) node.getUserObject();
            if (item.isExpanded()) {
                tree.expandPath(new TreePath(node.getPath()));
            }
        }
        
        Enumeration<TreeNode> children = node.children();
        while (children.hasMoreElements()) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) children.nextElement();
            expandNodesRecursively(child);
        }
    }
    
    /**
     * تنفيذ البحث
     */
    private void performSearch() {
        String searchText = searchField.getText().trim();
        
        if (searchText.isEmpty()) {
            // إعادة تحميل القائمة الكاملة
            loadMenuData();
            return;
        }
        
        // البحث في العناصر
        List<TreeMenuItem> searchResults = menuData.searchItems(searchText);
        
        // إنشاء شجرة البحث
        rootNode.removeAllChildren();
        
        if (!searchResults.isEmpty()) {
            DefaultMutableTreeNode searchNode = new DefaultMutableTreeNode("نتائج البحث");
            
            for (TreeMenuItem item : searchResults) {
                if (item.canAccess()) {
                    DefaultMutableTreeNode resultNode = new DefaultMutableTreeNode(item);
                    searchNode.add(resultNode);
                }
            }
            
            rootNode.add(searchNode);
            tree.expandPath(new TreePath(new Object[]{rootNode, searchNode}));
        }
        
        treeModel.reload();
    }
    
    /**
     * مصيّر خلايا الشجرة المخصص
     */
    private class TreeMenuCellRenderer extends DefaultTreeCellRenderer {
        
        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value,
                boolean selected, boolean expanded, boolean leaf, int row, boolean hasFocus) {
            
            super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row, hasFocus);
            
            if (value instanceof DefaultMutableTreeNode) {
                DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
                Object userObject = node.getUserObject();
                
                if (userObject instanceof TreeMenuItem) {
                    TreeMenuItem item = (TreeMenuItem) userObject;
                    
                    // تعيين النص
                    setText(item.getTitle());
                    
                    // تعيين الأيقونة
                    if (showIcons && item.getIcon() != null) {
                        setIcon(item.getIcon());
                    }
                    
                    // تعيين التلميح
                    if (showTooltips && item.getTooltip() != null) {
                        setToolTipText(item.getTooltip());
                    }
                    
                    // تعيين الألوان حسب الحالة
                    if (!item.isEnabled()) {
                        setForeground(Color.GRAY);
                    } else if (item.isFavorite()) {
                        setForeground(new Color(0, 100, 200));
                    }
                }
            }
            
            return this;
        }
    }
    
    // Getters and Setters
    public TreeMenuActionListener getActionListener() {
        return actionListener;
    }
    
    public void setActionListener(TreeMenuActionListener actionListener) {
        this.actionListener = actionListener;
    }
    
    public boolean isShowIcons() {
        return showIcons;
    }
    
    public void setShowIcons(boolean showIcons) {
        this.showIcons = showIcons;
        tree.repaint();
    }
    
    public boolean isShowTooltips() {
        return showTooltips;
    }
    
    public void setShowTooltips(boolean showTooltips) {
        this.showTooltips = showTooltips;
    }
    
    public boolean isSearchEnabled() {
        return searchEnabled;
    }
    
    public void setSearchEnabled(boolean searchEnabled) {
        this.searchEnabled = searchEnabled;
        toolbarPanel.setVisible(searchEnabled);
    }
    
    /**
     * تحديث القائمة
     */
    public void refreshMenu() {
        loadMenuData();
    }
    
    /**
     * مسح البحث
     */
    public void clearSearch() {
        searchField.setText("");
        loadMenuData();
    }
    
    /**
     * الحصول على العنصر المحدد
     */
    public TreeMenuItem getSelectedItem() {
        DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) tree.getLastSelectedPathComponent();
        if (selectedNode != null && selectedNode.getUserObject() instanceof TreeMenuItem) {
            return (TreeMenuItem) selectedNode.getUserObject();
        }
        return null;
    }

    /**
     * توسيع جميع العقد
     */
    public void expandAll() {
        expandAllNodes(rootNode);
    }

    /**
     * طي جميع العقد
     */
    public void collapseAll() {
        collapseAllNodes(rootNode);
    }

    /**
     * توسيع جميع العقد تكرارياً
     */
    private void expandAllNodes(DefaultMutableTreeNode node) {
        tree.expandPath(new TreePath(node.getPath()));

        Enumeration<TreeNode> children = node.children();
        while (children.hasMoreElements()) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) children.nextElement();
            expandAllNodes(child);
        }
    }

    /**
     * طي جميع العقد تكرارياً
     */
    private void collapseAllNodes(DefaultMutableTreeNode node) {
        Enumeration<TreeNode> children = node.children();
        while (children.hasMoreElements()) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) children.nextElement();
            collapseAllNodes(child);
        }

        if (node != rootNode) {
            tree.collapsePath(new TreePath(node.getPath()));
        }
    }

    /**
     * تحديد عنصر بالمعرف
     */
    public void selectItemById(String itemId) {
        TreeMenuItem item = menuData.findItemById(itemId);
        if (item != null) {
            DefaultMutableTreeNode node = findNodeByItem(rootNode, item);
            if (node != null) {
                TreePath path = new TreePath(node.getPath());
                tree.setSelectionPath(path);
                tree.scrollPathToVisible(path);
            }
        }
    }

    /**
     * البحث عن عقدة بالعنصر
     */
    private DefaultMutableTreeNode findNodeByItem(DefaultMutableTreeNode node, TreeMenuItem targetItem) {
        if (node.getUserObject() instanceof TreeMenuItem) {
            TreeMenuItem item = (TreeMenuItem) node.getUserObject();
            if (item.equals(targetItem)) {
                return node;
            }
        }

        Enumeration<TreeNode> children = node.children();
        while (children.hasMoreElements()) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) children.nextElement();
            DefaultMutableTreeNode found = findNodeByItem(child, targetItem);
            if (found != null) {
                return found;
            }
        }

        return null;
    }

    /**
     * إضافة أزرار إضافية لشريط الأدوات
     */
    public void addToolbarButtons() {
        JButton expandAllBtn = new JButton("توسيع الكل");
        expandAllBtn.setToolTipText("توسيع جميع العقد");
        expandAllBtn.addActionListener(e -> expandAll());

        JButton collapseAllBtn = new JButton("طي الكل");
        collapseAllBtn.setToolTipText("طي جميع العقد");
        collapseAllBtn.addActionListener(e -> collapseAll());

        JButton favoritesBtn = new JButton("المفضلة");
        favoritesBtn.setToolTipText("عرض العناصر المفضلة فقط");
        favoritesBtn.addActionListener(e -> showFavoritesOnly());

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.add(expandAllBtn);
        buttonPanel.add(collapseAllBtn);
        buttonPanel.add(favoritesBtn);

        toolbarPanel.add(buttonPanel, BorderLayout.WEST);
    }

    /**
     * عرض المفضلة فقط
     */
    private void showFavoritesOnly() {
        rootNode.removeAllChildren();

        List<TreeMenuItem> favorites = menuData.getFavoriteItems();
        if (!favorites.isEmpty()) {
            DefaultMutableTreeNode favoritesNode = new DefaultMutableTreeNode("العناصر المفضلة");

            for (TreeMenuItem item : favorites) {
                if (item.canAccess()) {
                    DefaultMutableTreeNode itemNode = new DefaultMutableTreeNode(item);
                    favoritesNode.add(itemNode);
                }
            }

            rootNode.add(favoritesNode);
            tree.expandPath(new TreePath(new Object[]{rootNode, favoritesNode}));
        }

        treeModel.reload();
    }
}
