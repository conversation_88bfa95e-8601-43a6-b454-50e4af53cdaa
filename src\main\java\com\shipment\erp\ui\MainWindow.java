package com.shipment.erp.ui;

import com.shipment.erp.model.ui.TreeMenuItem;
import com.shipment.erp.model.ui.TreeMenuData;
import com.shipment.erp.service.TreeMenuService;
import com.shipment.erp.ui.components.AdvancedTreeMenu;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

/**
 * النافذة الرئيسية للتطبيق
 * تحتوي على القائمة الشجرية ولوحة المحتوى الرئيسية
 */
public class MainWindow extends JFrame {
    
    private final TreeMenuService menuService;
    private final TreeMenuData menuData;
    private final AdvancedTreeMenu treeMenu;
    
    // مكونات الواجهة
    private JSplitPane mainSplitPane;
    private JPanel contentPanel;
    private JPanel statusPanel;
    private JLabel statusLabel;
    private JMenuBar menuBar;
    private JToolBar toolBar;
    
    // خصائص النافذة
    private static final String WINDOW_TITLE = "نظام إدارة الشحنات - Ship ERP";
    private static final int DEFAULT_WIDTH = 1200;
    private static final int DEFAULT_HEIGHT = 800;
    
    /**
     * منشئ النافذة الرئيسية
     */
    public MainWindow() {
        // تهيئة الخدمات
        this.menuService = new TreeMenuService();
        this.menuData = menuService.getMenuData();
        this.treeMenu = new AdvancedTreeMenu(menuData);
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupMenuBar();
        setupToolBar();
        setupStatusBar();
        
        // إعدادات النافذة
        setTitle(WINDOW_TITLE);
        setSize(DEFAULT_WIDTH, DEFAULT_HEIGHT);
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        setLocationRelativeTo(null);
        
        // تطبيق Look and Feel
        applyLookAndFeel();
        
        // تحديث صلاحيات القائمة
        menuService.updateMenuPermissions();
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // لوحة المحتوى الرئيسية
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // إضافة رسالة ترحيب افتراضية
        JLabel welcomeLabel = new JLabel("<html><div style='text-align: center;'>" +
                "<h1>مرحباً بك في نظام إدارة الشحنات</h1>" +
                "<p>اختر عنصراً من القائمة الجانبية للبدء</p>" +
                "</div></html>", SwingConstants.CENTER);
        welcomeLabel.setFont(welcomeLabel.getFont().deriveFont(Font.PLAIN, 14f));
        contentPanel.add(welcomeLabel, BorderLayout.CENTER);
        
        // الفاصل الرئيسي
        mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        mainSplitPane.setLeftComponent(treeMenu);
        mainSplitPane.setRightComponent(contentPanel);
        mainSplitPane.setDividerLocation(300);
        mainSplitPane.setResizeWeight(0.0);
        
        // شريط الحالة
        statusPanel = new JPanel(new BorderLayout());
        statusLabel = new JLabel("جاهز");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        add(mainSplitPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    private void setupEventHandlers() {
        // مستمع إغلاق النافذة
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                exitApplication();
            }
        });
        
        // مستمع أحداث القائمة الشجرية
        treeMenu.setActionListener(new AdvancedTreeMenu.TreeMenuActionListener() {
            @Override
            public void onItemSelected(TreeMenuItem item) {
                updateStatusBar("تم تحديد: " + item.getTitle());
            }
            
            @Override
            public void onItemDoubleClicked(TreeMenuItem item) {
                openMenuItem(item);
            }
            
            @Override
            public void onItemRightClicked(TreeMenuItem item, Point location) {
                showContextMenu(item, location);
            }
        });
    }
    
    /**
     * إعداد شريط القوائم
     */
    private void setupMenuBar() {
        menuBar = new JMenuBar();
        
        // قائمة ملف
        JMenu fileMenu = new JMenu("ملف");
        fileMenu.add(createMenuItem("جديد", "Ctrl+N", e -> newDocument()));
        fileMenu.add(createMenuItem("فتح", "Ctrl+O", e -> openDocument()));
        fileMenu.addSeparator();
        fileMenu.add(createMenuItem("خروج", "Alt+F4", e -> exitApplication()));
        
        // قائمة عرض
        JMenu viewMenu = new JMenu("عرض");
        JCheckBoxMenuItem showIconsItem = new JCheckBoxMenuItem("إظهار الأيقونات", treeMenu.isShowIcons());
        showIconsItem.addActionListener(e -> treeMenu.setShowIcons(showIconsItem.isSelected()));
        viewMenu.add(showIconsItem);
        
        JCheckBoxMenuItem showTooltipsItem = new JCheckBoxMenuItem("إظهار التلميحات", treeMenu.isShowTooltips());
        showTooltipsItem.addActionListener(e -> treeMenu.setShowTooltips(showTooltipsItem.isSelected()));
        viewMenu.add(showTooltipsItem);
        
        // قائمة مساعدة
        JMenu helpMenu = new JMenu("مساعدة");
        helpMenu.add(createMenuItem("حول البرنامج", null, e -> showAboutDialog()));
        
        menuBar.add(fileMenu);
        menuBar.add(viewMenu);
        menuBar.add(helpMenu);
        
        setJMenuBar(menuBar);
    }
    
    /**
     * إعداد شريط الأدوات
     */
    private void setupToolBar() {
        toolBar = new JToolBar();
        toolBar.setFloatable(false);
        
        // أزرار شريط الأدوات
        toolBar.add(createToolBarButton("جديد", "/images/new.png", e -> newDocument()));
        toolBar.add(createToolBarButton("فتح", "/images/open.png", e -> openDocument()));
        toolBar.addSeparator();
        toolBar.add(createToolBarButton("تحديث", "/images/refresh.png", e -> refreshMenu()));
        
        add(toolBar, BorderLayout.NORTH);
    }
    
    /**
     * إعداد شريط الحالة
     */
    private void setupStatusBar() {
        // إضافة معلومات إضافية لشريط الحالة
        JLabel timeLabel = new JLabel();
        Timer timer = new Timer(1000, e -> {
            timeLabel.setText(java.time.LocalTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")));
        });
        timer.start();
        
        statusPanel.add(timeLabel, BorderLayout.EAST);
    }
    
    /**
     * إنشاء عنصر قائمة
     */
    private JMenuItem createMenuItem(String text, String accelerator, ActionListener action) {
        JMenuItem item = new JMenuItem(text);
        if (accelerator != null) {
            item.setAccelerator(KeyStroke.getKeyStroke(accelerator));
        }
        item.addActionListener(action);
        return item;
    }
    
    /**
     * إنشاء زر شريط أدوات
     */
    private JButton createToolBarButton(String text, String iconPath, ActionListener action) {
        JButton button = new JButton(text);
        
        // محاولة تحميل الأيقونة
        try {
            java.net.URL iconUrl = getClass().getResource(iconPath);
            if (iconUrl != null) {
                ImageIcon icon = new ImageIcon(iconUrl);
                button.setIcon(icon);
                button.setText(null); // إخفاء النص إذا كانت الأيقونة متوفرة
            }
        } catch (Exception e) {
            // الاحتفاظ بالنص إذا فشل تحميل الأيقونة
        }
        
        button.setToolTipText(text);
        button.addActionListener(action);
        return button;
    }
    
    /**
     * فتح عنصر القائمة
     */
    private void openMenuItem(TreeMenuItem item) {
        updateStatusBar("فتح: " + item.getTitle());
        
        // هنا يمكن إضافة منطق فتح النوافذ المختلفة حسب نوع العنصر
        switch (item.getActionType()) {
            case "VIEW":
                openView(item);
                break;
            case "DIALOG":
                openDialog(item);
                break;
            case "REPORT":
                openReport(item);
                break;
            default:
                showMessage("لم يتم تنفيذ هذه الوظيفة بعد: " + item.getTitle());
        }
    }
    
    /**
     * فتح عرض
     */
    private void openView(TreeMenuItem item) {
        // إنشاء لوحة جديدة للمحتوى
        JPanel viewPanel = new JPanel(new BorderLayout());
        viewPanel.setBorder(BorderFactory.createTitledBorder(item.getTitle()));
        
        JLabel contentLabel = new JLabel("<html><div style='text-align: center;'>" +
                "<h2>" + item.getTitle() + "</h2>" +
                "<p>" + item.getDescription() + "</p>" +
                "<p>المسار: " + item.getTargetView() + "</p>" +
                "</div></html>", SwingConstants.CENTER);
        
        viewPanel.add(contentLabel, BorderLayout.CENTER);
        
        // استبدال المحتوى
        contentPanel.removeAll();
        contentPanel.add(viewPanel, BorderLayout.CENTER);
        contentPanel.revalidate();
        contentPanel.repaint();
    }
    
    /**
     * فتح حوار
     */
    private void openDialog(TreeMenuItem item) {
        JOptionPane.showMessageDialog(this, 
                "فتح حوار: " + item.getTitle() + "\n" + item.getDescription(),
                item.getTitle(), 
                JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * فتح تقرير
     */
    private void openReport(TreeMenuItem item) {
        JOptionPane.showMessageDialog(this, 
                "فتح تقرير: " + item.getTitle() + "\n" + item.getDescription(),
                item.getTitle(), 
                JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * إظهار قائمة السياق
     */
    private void showContextMenu(TreeMenuItem item, Point location) {
        JPopupMenu contextMenu = new JPopupMenu();
        
        contextMenu.add(createMenuItem("فتح", null, e -> openMenuItem(item)));
        contextMenu.addSeparator();
        
        JCheckBoxMenuItem favoriteItem = new JCheckBoxMenuItem("مفضل", item.isFavorite());
        favoriteItem.addActionListener(e -> {
            item.setFavorite(favoriteItem.isSelected());
            treeMenu.refreshMenu();
        });
        contextMenu.add(favoriteItem);
        
        contextMenu.add(createMenuItem("خصائص", null, e -> showItemProperties(item)));
        
        contextMenu.show(treeMenu, location.x, location.y);
    }
    
    /**
     * إظهار خصائص العنصر
     */
    private void showItemProperties(TreeMenuItem item) {
        String properties = String.format(
                "المعرف: %s\nالعنوان: %s\nالوصف: %s\nالمسار: %s\nعدد الوصول: %d",
                item.getId(), item.getTitle(), item.getDescription(), 
                item.getFullPath(), item.getAccessCount());
        
        JOptionPane.showMessageDialog(this, properties, "خصائص العنصر", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * تحديث شريط الحالة
     */
    private void updateStatusBar(String message) {
        statusLabel.setText(message);
    }
    
    /**
     * تطبيق مظهر النافذة
     */
    private void applyLookAndFeel() {
        try {
            // استخدام مظهر النظام
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            System.err.println("فشل في تطبيق مظهر النافذة: " + e.getMessage());
        }
    }
    
    // إجراءات القوائم
    private void newDocument() {
        updateStatusBar("إنشاء مستند جديد");
    }
    
    private void openDocument() {
        updateStatusBar("فتح مستند");
    }
    
    private void refreshMenu() {
        treeMenu.refreshMenu();
        updateStatusBar("تم تحديث القائمة");
    }
    
    private void showAboutDialog() {
        JOptionPane.showMessageDialog(this,
                "نظام إدارة الشحنات\nالإصدار 1.0\n\nتطوير: فريق التطوير",
                "حول البرنامج",
                JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "رسالة", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void exitApplication() {
        int result = JOptionPane.showConfirmDialog(this,
                "هل تريد إغلاق التطبيق؟",
                "تأكيد الإغلاق",
                JOptionPane.YES_NO_OPTION);
        
        if (result == JOptionPane.YES_OPTION) {
            // حفظ إعدادات المستخدم
            menuService.saveMenuConfiguration();
            System.exit(0);
        }
    }
    
    /**
     * تشغيل التطبيق
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new MainWindow().setVisible(true);
        });
    }
}
