import com.shipment.erp.model.ui.TreeMenuItem;
import com.shipment.erp.model.ui.TreeMenuData;
import com.shipment.erp.ui.components.AdvancedTreeMenu;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * تطبيق تجريبي للقائمة الشجرية المتقدمة
 * يعرض جميع ميزات القائمة الشجرية
 */
public class TreeMenuDemo extends JFrame {
    
    private TreeMenuData menuData;
    private AdvancedTreeMenu treeMenu;
    private JPanel contentPanel;
    private JLabel statusLabel;
    
    public TreeMenuDemo() {
        initializeDemo();
        setupUI();
        setupEventHandlers();
        
        setTitle("تجربة القائمة الشجرية المتقدمة - Ship ERP");
        setSize(1000, 700);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        
        // تطبيق مظهر النظام
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * تهيئة البيانات التجريبية
     */
    private void initializeDemo() {
        menuData = new TreeMenuData();
        
        // إضافة بيانات تجريبية إضافية
        addDemoData();
        
        treeMenu = new AdvancedTreeMenu(menuData);
    }
    
    /**
     * إضافة بيانات تجريبية
     */
    private void addDemoData() {
        // قسم المبيعات
        TreeMenuItem salesRoot = new TreeMenuItem("sales", "إدارة المبيعات", 
                "Sales Management", "إدارة عمليات البيع والعملاء",
                "/images/sales.png", "CATEGORY", null);
        salesRoot.setCategory("SALES");
        salesRoot.setSortOrder(6);
        
        TreeMenuItem customers = new TreeMenuItem("sales.customers", "إدارة العملاء",
                "Customer Management", "إدارة بيانات العملاء",
                "/images/customers.png", "VIEW", "/fxml/sales/customers.fxml");
        customers.setRequiredPermission("SALES_CUSTOMERS");
        customers.setTooltip("إدارة قاعدة بيانات العملاء");
        
        TreeMenuItem orders = new TreeMenuItem("sales.orders", "إدارة الطلبات",
                "Order Management", "إدارة طلبات العملاء",
                "/images/orders.png", "VIEW", "/fxml/sales/orders.fxml");
        orders.setRequiredPermission("SALES_ORDERS");
        orders.setTooltip("متابعة وإدارة طلبات العملاء");
        
        TreeMenuItem invoices = new TreeMenuItem("sales.invoices", "الفواتير",
                "Invoices", "إدارة فواتير المبيعات",
                "/images/invoices.png", "VIEW", "/fxml/sales/invoices.fxml");
        invoices.setRequiredPermission("SALES_INVOICES");
        invoices.setTooltip("إنشاء ومتابعة فواتير المبيعات");
        
        salesRoot.addChild(customers);
        salesRoot.addChild(orders);
        salesRoot.addChild(invoices);
        
        // قسم المشتريات
        TreeMenuItem purchasesRoot = new TreeMenuItem("purchases", "إدارة المشتريات",
                "Purchases Management", "إدارة عمليات الشراء والموردين",
                "/images/purchases.png", "CATEGORY", null);
        purchasesRoot.setCategory("PURCHASES");
        purchasesRoot.setSortOrder(7);
        
        TreeMenuItem purchaseOrders = new TreeMenuItem("purchases.orders", "أوامر الشراء",
                "Purchase Orders", "إدارة أوامر الشراء",
                "/images/purchase-orders.png", "VIEW", "/fxml/purchases/orders.fxml");
        purchaseOrders.setRequiredPermission("PURCHASES_ORDERS");
        
        TreeMenuItem receivingNotes = new TreeMenuItem("purchases.receiving", "إشعارات الاستلام",
                "Receiving Notes", "تسجيل استلام البضائع",
                "/images/receiving.png", "VIEW", "/fxml/purchases/receiving.fxml");
        receivingNotes.setRequiredPermission("PURCHASES_RECEIVING");
        
        purchasesRoot.addChild(purchaseOrders);
        purchasesRoot.addChild(receivingNotes);
        
        // قسم المحاسبة
        TreeMenuItem accountingRoot = new TreeMenuItem("accounting", "المحاسبة",
                "Accounting", "النظام المحاسبي والمالي",
                "/images/accounting.png", "CATEGORY", null);
        accountingRoot.setCategory("ACCOUNTING");
        accountingRoot.setSortOrder(8);
        
        TreeMenuItem chartOfAccounts = new TreeMenuItem("accounting.chart", "دليل الحسابات",
                "Chart of Accounts", "إدارة دليل الحسابات",
                "/images/chart-accounts.png", "VIEW", "/fxml/accounting/chart.fxml");
        chartOfAccounts.setRequiredPermission("ACCOUNTING_CHART");
        
        TreeMenuItem journalEntries = new TreeMenuItem("accounting.journal", "القيود اليومية",
                "Journal Entries", "تسجيل القيود المحاسبية",
                "/images/journal.png", "VIEW", "/fxml/accounting/journal.fxml");
        journalEntries.setRequiredPermission("ACCOUNTING_JOURNAL");
        
        TreeMenuItem trialBalance = new TreeMenuItem("accounting.trial", "ميزان المراجعة",
                "Trial Balance", "عرض ميزان المراجعة",
                "/images/trial-balance.png", "REPORT", "/reports/trial-balance.fxml");
        trialBalance.setRequiredPermission("ACCOUNTING_REPORTS");
        
        accountingRoot.addChild(chartOfAccounts);
        accountingRoot.addChild(journalEntries);
        accountingRoot.addChild(trialBalance);
        
        // إضافة الأقسام الجديدة
        menuData.addRootItem(salesRoot);
        menuData.addRootItem(purchasesRoot);
        menuData.addRootItem(accountingRoot);
        
        // تعيين بعض العناصر كمفضلة
        customers.setFavorite(true);
        orders.setFavorite(true);
        chartOfAccounts.setFavorite(true);
        
        // محاكاة عدادات الوصول
        customers.setAccessCount(15);
        orders.setAccessCount(12);
        invoices.setAccessCount(8);
        chartOfAccounts.setAccessCount(20);
        journalEntries.setAccessCount(5);
    }
    
    /**
     * إعداد واجهة المستخدم
     */
    private void setupUI() {
        setLayout(new BorderLayout());
        
        // إنشاء لوحة المحتوى
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBorder(BorderFactory.createTitledBorder("منطقة المحتوى"));
        
        JLabel welcomeLabel = new JLabel("<html><div style='text-align: center;'>" +
                "<h1>مرحباً بك في تجربة القائمة الشجرية</h1>" +
                "<p>اختر عنصراً من القائمة الجانبية لعرض محتواه</p>" +
                "<br><p><b>الميزات المتاحة:</b></p>" +
                "<ul>" +
                "<li>البحث في القائمة</li>" +
                "<li>النقر المزدوج لفتح العناصر</li>" +
                "<li>النقر بالزر الأيمن لقائمة السياق</li>" +
                "<li>إضافة/إزالة المفضلة</li>" +
                "<li>عرض خصائص العناصر</li>" +
                "</ul>" +
                "</div></html>", SwingConstants.CENTER);
        
        contentPanel.add(welcomeLabel, BorderLayout.CENTER);
        
        // إنشاء الفاصل
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setLeftComponent(treeMenu);
        splitPane.setRightComponent(contentPanel);
        splitPane.setDividerLocation(350);
        splitPane.setResizeWeight(0.0);
        
        // شريط الحالة
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusLabel = new JLabel("جاهز - اختر عنصراً من القائمة");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        // شريط أدوات تجريبي
        JToolBar toolBar = createDemoToolBar();
        
        add(toolBar, BorderLayout.NORTH);
        add(splitPane, BorderLayout.CENTER);
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء شريط أدوات تجريبي
     */
    private JToolBar createDemoToolBar() {
        JToolBar toolBar = new JToolBar();
        toolBar.setFloatable(false);
        
        JButton refreshBtn = new JButton("تحديث القائمة");
        refreshBtn.addActionListener(e -> {
            treeMenu.refreshMenu();
            updateStatus("تم تحديث القائمة");
        });
        
        JButton clearSearchBtn = new JButton("مسح البحث");
        clearSearchBtn.addActionListener(e -> {
            treeMenu.clearSearch();
            updateStatus("تم مسح البحث");
        });
        
        JButton favoritesBtn = new JButton("العناصر المفضلة");
        favoritesBtn.addActionListener(e -> showFavorites());
        
        JButton mostUsedBtn = new JButton("الأكثر استخداماً");
        mostUsedBtn.addActionListener(e -> showMostUsed());
        
        toolBar.add(refreshBtn);
        toolBar.addSeparator();
        toolBar.add(clearSearchBtn);
        toolBar.addSeparator();
        toolBar.add(favoritesBtn);
        toolBar.add(mostUsedBtn);
        
        return toolBar;
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    private void setupEventHandlers() {
        treeMenu.setActionListener(new AdvancedTreeMenu.TreeMenuActionListener() {
            @Override
            public void onItemSelected(TreeMenuItem item) {
                updateStatus("تم تحديد: " + item.getTitle());
                showItemInfo(item);
            }
            
            @Override
            public void onItemDoubleClicked(TreeMenuItem item) {
                updateStatus("تم فتح: " + item.getTitle());
                openItem(item);
            }
            
            @Override
            public void onItemRightClicked(TreeMenuItem item, Point location) {
                updateStatus("قائمة السياق لـ: " + item.getTitle());
                // القائمة السياق يتم التعامل معها في AdvancedTreeMenu
            }
        });
    }
    
    /**
     * عرض معلومات العنصر
     */
    private void showItemInfo(TreeMenuItem item) {
        contentPanel.removeAll();
        
        JPanel infoPanel = new JPanel(new BorderLayout());
        infoPanel.setBorder(BorderFactory.createTitledBorder("معلومات العنصر"));
        
        String info = String.format("<html><div style='padding: 20px;'>" +
                "<h2>%s</h2>" +
                "<p><b>المعرف:</b> %s</p>" +
                "<p><b>الوصف:</b> %s</p>" +
                "<p><b>النوع:</b> %s</p>" +
                "<p><b>المسار الكامل:</b> %s</p>" +
                "<p><b>عدد مرات الوصول:</b> %d</p>" +
                "<p><b>مفضل:</b> %s</p>" +
                "<p><b>الصلاحية المطلوبة:</b> %s</p>" +
                "</div></html>",
                item.getTitle(),
                item.getId(),
                item.getDescription() != null ? item.getDescription() : "غير محدد",
                item.getActionType() != null ? item.getActionType() : "غير محدد",
                item.getFullPath(),
                item.getAccessCount(),
                item.isFavorite() ? "نعم" : "لا",
                item.getRequiredPermission() != null ? item.getRequiredPermission() : "غير مطلوبة");
        
        JLabel infoLabel = new JLabel(info);
        infoPanel.add(infoLabel, BorderLayout.CENTER);
        
        contentPanel.add(infoPanel, BorderLayout.CENTER);
        contentPanel.revalidate();
        contentPanel.repaint();
    }
    
    /**
     * فتح العنصر
     */
    private void openItem(TreeMenuItem item) {
        JOptionPane.showMessageDialog(this,
                "تم فتح العنصر: " + item.getTitle() + "\n" +
                "النوع: " + item.getActionType() + "\n" +
                "المسار: " + item.getTargetView(),
                "فتح العنصر",
                JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * عرض العناصر المفضلة
     */
    private void showFavorites() {
        java.util.List<TreeMenuItem> favorites = menuData.getFavoriteItems();
        StringBuilder sb = new StringBuilder("العناصر المفضلة:\n\n");
        
        if (favorites.isEmpty()) {
            sb.append("لا توجد عناصر مفضلة");
        } else {
            for (TreeMenuItem item : favorites) {
                sb.append("• ").append(item.getTitle())
                  .append(" (").append(item.getAccessCount()).append(" مرة)")
                  .append("\n");
            }
        }
        
        JOptionPane.showMessageDialog(this, sb.toString(), "العناصر المفضلة", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * عرض العناصر الأكثر استخداماً
     */
    private void showMostUsed() {
        java.util.List<TreeMenuItem> mostUsed = menuData.getMostUsedItems(5);
        StringBuilder sb = new StringBuilder("العناصر الأكثر استخداماً:\n\n");
        
        if (mostUsed.isEmpty()) {
            sb.append("لا توجد إحصائيات استخدام");
        } else {
            for (int i = 0; i < mostUsed.size(); i++) {
                TreeMenuItem item = mostUsed.get(i);
                sb.append((i + 1)).append(". ").append(item.getTitle())
                  .append(" (").append(item.getAccessCount()).append(" مرة)")
                  .append("\n");
            }
        }
        
        JOptionPane.showMessageDialog(this, sb.toString(), "الأكثر استخداماً", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * تحديث شريط الحالة
     */
    private void updateStatus(String message) {
        statusLabel.setText(message);
    }
    
    /**
     * تشغيل التطبيق التجريبي
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new TreeMenuDemo().setVisible(true);
        });
    }
}
